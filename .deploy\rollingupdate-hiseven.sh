#!/bin/sh
PROJECT="baymax"
SERVICE="cloudseven"
REGION_ID="ap-southeast-1"
SCALING_GROUP_NAME="$PROJECT-$SERVICE-$ENV-ess"
SCALING_GROUP_ID=$(aliyun ess DescribeScalingGroups --RegionId $REGION_ID --ScalingGroupName "$SCALING_GROUP_NAME" | jq -r '.ScalingGroups.ScalingGroup[] | .ScalingGroupId')
SCALING_CONFIGURATION_NAME="$PROJECT-$SERVICE-$ENV-sc"
SCALING_CONFIGURATION_ID=$(aliyun ess DescribeEciScalingConfigurations --RegionId $REGION_ID --ScalingGroupId "$SCALING_GROUP_ID" | jq -r --arg SC_NAME "$SCALING_CONFIGURATION_NAME" '.ScalingConfigurations[] | select(.ScalingConfigurationName == $SC_NAME) | .ScalingConfigurationId')
echo "Scaling Group ID: $SCALING_GROUP_ID"
echo "Scaling Configuration ID: $SCALING_CONFIGURATION_ID"

aliyun oos StartExecution \
  --region $REGION_ID \
  --RegionId ${REGION_ID} \
  --TemplateName 'ACS-ESS-RollingUpdateByUpdateContainerGroup' \
  --Parameters "{\"invokeType\":\"invoke\",\"scalingGroupId\":\"${SCALING_GROUP_ID}\",\"enterProcess\":[\"ScheduledAction\"],\"exitProcess\":[\"ScheduledAction\"],
  \"containerConfigure\":[
    {
      \"Memory\": 4,
      \"Cpu\": 2,
      \"Image\": \"hiseven-registry-vpc.ap-southeast-1.cr.aliyuncs.com/${PROJECT}/${SERVICE}:${VERSION}\",
      \"Name\": \"${PROJECT}-${SERVICE}-${ENV}\",
      \"Volumes\": [],
      \"ImagePullPolicy\": \"IfNotPresent\",
      \"Command\": [],
      \"Arg\": [],
      \"VolumeMount\": [],
      \"EnvironmentVar\": [
        {
          \"Key\": \"VERSION\",
          \"Value\": \"${VERSION}\"
        },
        {
          \"Key\": \"SERVICE_NAME\",
          \"Value\": \"${SERVICE_NAME}\"
        }
      ]
    }
  ],
  \"sourceContainerConfigure\":[
    {
      \"Memory\": 4,
      \"Cpu\": 2,
      \"Image\": \"hiseven-registry-vpc.ap-southeast-1.cr.aliyuncs.com/${PROJECT}/${SERVICE}:${VERSION}\",
      \"Name\": \"${PROJECT}-${SERVICE}-${ENV}\",
      \"Volumes\": [],
      \"ImagePullPolicy\": \"IfNotPresent\",
      \"Command\": [],
      \"Arg\": [],
      \"VolumeMount\": [],
      \"EnvironmentVar\": [
        {
          \"Key\": \"VERSION\",
          \"Value\": \"${VERSION}\"
        },
        {
          \"Key\": \"SERVICE_NAME\",
          \"Value\": \"${SERVICE_NAME}\"
        }
      ]
    }
  ],
  \"updateType\":\"IncrementalUpdate\",
  \"sourceExecutionId\":\"\",
  \"scalingConfigurationId\":\"${SCALING_CONFIGURATION_ID}\",
  \"batchPauseOption\":\"Automatic\",
  \"batchNumber\":3,
  \"maxErrors\":0,
  \"OOSAssumeRole\":\"\"}" \
  --Description '' \
  --Mode Automatic \
  --Tags "{\"Scaling Group\":\"${SCALING_GROUP_NAME}\",\"Env\":\"${ENV}\"}"
