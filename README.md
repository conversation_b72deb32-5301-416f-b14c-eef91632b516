# Project Setup

## Install Dependencies

```bash
pnpm i
```

## Start Development Server

```bash
pnpm dev # run all development tasks
# or
pnpm cloudseven:dev
```

## Install package in project/workspace

```bash
# Change to the path you want
cd packages/cloudseven

# Add/install with flag '-w' to install in workspace

pnpm install [packages] -w
```

## Update Translation

1. Copy from `env.template`, rename it to `.env` and move to auto folder

2. Refer the [link](https://cloud.google.com/iam/docs/keys-create-delete#iam-service-account-keys-create-console) to create credentials.json

   2a. Create Project

   - Use company account log in to Google Cloud Console.
   - Click the navigation menu in the upper left corner and select `IAM & Admin` > `Manage Resources`.
   - Click `Create Project`, enter a project name (e.g. My Drive API Project) and click `Create`.

   2b. Enable Google Drive API and Google Sheet API

   - Click `APIs & Services` > `Library`.
   - Search for Google Drive API & Google Sheet API and click `Enable` button.

   2c. Create a service account

   - Click `APIs & Services` > `Credentials`.
   - Click `Create Credentials` and select `Service account`.
   - Fill in the service account information
   - Name (e.g drive-uploader-service-account)
   - Description (e.g. Service account for uploading files to Drive)
   - Click `Create and Continue`.
   - Permissions can select `Editor` or `Owner` role, then click `Continue`.
   - Complete the service account creation and click `Done`.

   2d. Download credentials.json

   - Click `APIs & Services` > `Credentials` page, find the service account you created.
   - Click `Manage keys` on the right.
   - Click `Add Key` > `Create New Key` and select JSON.
   - Download credentials.json file.

3. Once the credentials.json downloaded, copy the key and value at below to `.env` file.

   - Project_id
   - Private_key_id
   - Private_key
   - Client_email
   - Client_id

4. If upload JSON file to Google Sheet, run `node json-to-g-sheet.cjs` in auto folder
   (Please update google sheet name and recipientEmails when converting)

5. If download Google Sheet to JSON file, run `node g-sheet-to-json.cjs` in auto folder
   (Please update SPREADSHEET_ID when converting)

## FAQ - Update Translate

### 1. What should I do if the designer updates the EN/ZH part?

After the designer or engineer updates the translation in Google Sheets, make sure the file is saved. Then, run `node g-sheet-to-json.cjs`, and the `en.json` and `zh.json` files in the `dictionaries` folder will be automatically updated.

### 2. If we added new key, What should our team do?

Run `node g-sheet-to-json.cjs`, and the `en.json` and `zh.json` files in the `dictionaries` folder will automatically update with the new key.

### 3. What if both a new page launch (new key) and an existing page update (existing key) happen at the same time?

Since both designers and engineers work directly in Google Sheets, any modifications or updates should be done within the sheet. After the updates are complete, the engineer can run `node g-sheet-to-json.cjs` to convert the updated Google Sheet to JSON.

### To run local host

### Open Integrated Terminal (Right click on the folder)

Path: packages/cloudseven

Run: pnpm ./auto/dev dev
