FROM hiseven-registry-vpc.ap-southeast-1.cr.aliyuncs.com/base/gitlab/aliyuncli:node AS builder

WORKDIR /app

ARG VERSION=unknown \
    ENV=stg \
    ALICLOUD_ENDPOINT \
    ALICLOUD_ACCESS_KEY_ID \
    ALICLOUD_SECRET_ACCESS_KEY \
    ALICLOUD_DEFAULT_REGION \
    NPM_TOKEN \

ENV VERSION=$VERSION \
    ENV=$ENV \
    NPM_TOKEN=$NPM_TOKEN \
    ASSET_PREFIX=https://media.hiseven.com/cloudseven

COPY . .

RUN ls -l packages/cloudseven/auto
RUN chmod +x packages/cloudseven/auto/build

RUN pnpm install
RUN pnpm cloudseven:build:$ENV
RUN ossutil64 config --access-key-id $ALICLOUD_ACCESS_KEY_ID --access-key-secret $ALICLOUD_SECRET_ACCESS_KEY --endpoint $ALICLOUD_ENDPOINT
RUN ossutil64 cp -r -u packages/cloudseven/.next/static oss://assets-hiseven/cloudseven/_next/static

FROM node:20-alpine as runtime

WORKDIR /app

COPY --from=builder /app/packages/cloudseven/.next/standalone/packages/cloudseven ./
COPY --from=builder /app/packages/cloudseven/.next/standalone ./

RUN rm -rf /app/packages

EXPOSE 3000
CMD ["sh", "-c", "node server.js"]
