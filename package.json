{"name": "root", "private": true, "scripts": {"format": "prettier --write */**/*.{js,jsx,json,ts,tsx,scss,css,md}", "cloudseven:build": "lerna run build --scope=cloudseven", "cloudseven:build:prod": "lerna run build:prod --scope=cloudseven", "cloudseven:build:stg": "lerna run build:stg --scope=cloudseven", "cloudseven:dev": "lerna run dev --scope=cloudseven", "cloudseven:dev:prod": "lerna run dev:prod --scope=cloudseven", "cloudseven:dev:stg": "lerna run dev:stg --scope=cloudseven", "cloudseven:start": "lerna run start --scope=cloudseven", "cloudseven:i18n:pull": "lerna run i18n:pull --scope=cloudseven", "cloudseven:i18n:push": "lerna run i18n:push --scope=cloudseven", "lint": "echo lint", "prepare": "husky"}, "dependencies": {"@barba/core": "^2.10.3", "@commitlint/config-conventional": "^19.2.2", "@hookform/resolvers": "^3.9.0", "@next/font": "^14.2.15", "@next/third-parties": "^14.2.15", "@tanstack/react-query": "^5.54.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "dotenv-cli": "^7.3.0", "embla-carousel": "^8.3.0", "embla-carousel-auto-scroll": "^8.3.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.23.6", "gsap": "^3.13.0", "html-react-parser": "^5.1.18", "lenis": "^1.3.8", "motion": "^11.11.15", "next": "14.2.5", "next-nprogress-bar": "^2.3.14", "next-translate": "^2.6.2", "react": "^18.3.1", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-lottie-player": "^2.1.0", "react-modal": "^3.16.3", "remark-gfm": "^4.0.0", "sharp": "^0.33.5", "yup": "^1.4.0", "zustand": "^4.5.4"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@next/bundle-analyzer": "^14.2.15", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.1.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.0.1", "@typescript-eslint/parser": "^8.1.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^15.0.0 || ^16.0.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.34.0", "husky": "^9.1.4", "lerna": "^8.1.8", "lint-staged": "^15.2.8", "nx": "19.5.6", "postcss": "^8.4.41", "prettier": "3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "raw-loader": "^4.0.2", "tailwindcss": "^4.1.6", "typescript": "^5"}}