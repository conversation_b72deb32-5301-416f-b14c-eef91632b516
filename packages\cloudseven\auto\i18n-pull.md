# Syncing Translations from Google Sheets

This project includes a script to synchronize translation data from a Google Sheet into the local `zh.json` dictionary file. The script is designed to work with Google Sheets using a service account and expects credentials to be provided via environment variables.

## Prerequisites

- Node.js installed
- Google Cloud service account with access to Google Drive and Sheets APIs
- The following environment variables set in your `.env` file:
  - `GOOGLE_PROJECT_ID`
  - `GOOGLE_PRIVATE_KEY_ID`
  - `GOOGLE_PRIVATE_KEY`
  - `GOOGLE_CLIENT_EMAIL`
  - `GOOGLE_CLIENT_ID`

## How It Works

1. **Google API Authentication**  
   The script authenticates with Google APIs using the provided service account credentials.

2. **Branch Detection**  
   It detects the current Git branch and uses the branch name as the target tab (sheet) in the Google Sheet. If the tab does not exist, it falls back to a default tab named `master`.

3. **Fetching Data**  
   The script locates the Google Sheet named `cloudsevenproxy Translation` and reads translation key-value pairs from the appropriate tab.

4. **Processing Data**

   - Skips the first two rows (assumed to be metadata).
   - Handles special cases where keys or values end with `<end_space>`, converting them to a trailing space.
   - Optionally formats the output for readability if certain keys are present.

5. **Writing Output**  
   The processed translations are written to `src/dictionaries/zh.json`.

## Usage

1. Ensure your `.env` file is configured with the required Google credentials.
2. Run the script:

   ```bash
   pnpm run cloudseven:i18n:pull
   ```

   Replace `path/to/your/script.js` with the actual path to the script file.

3. On success, your local `zh.json` will be updated with the latest translations from Google Sheets.

## Troubleshooting

- If required environment variables are missing, the script will exit with an error.
- If the Google Sheet or the specified tab is not found, appropriate warnings or errors will be displayed.

## Notes

- Sheet/tab names cannot contain the following characters: `[ ] : * ? / \` and must be 100 characters or fewer.
- The script is intended for use in a development workflow where translation updates are managed via Google Sheets.
