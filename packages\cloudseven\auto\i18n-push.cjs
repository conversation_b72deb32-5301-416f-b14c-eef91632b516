const { google } = require('googleapis');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

require('dotenv').config();

const FILE_NAME = 'Elfproxy Translation';
const EXTERNAL_EMAILS = []; // not in hiseven.com domain
const DEFAULT_BRANCH = 'master';

const COLUMN_WIDTHS = [600, 1200];
const KEYS_TO_EXCLUDES = ['__SKIP_en_last_sync', '__SKIP_en_sheet_url'];

async function getGoogleClient() {
  const credentials = {
    type: 'service_account',
    project_id: process.env.GOOGLE_PROJECT_ID,
    private_key_id: process.env.GOOGLE_PRIVATE_KEY_ID,
    private_key: process.env.GOOGLE_PRIVATE_KEY.replace(/\\n/g, '\n'),
    client_email: process.env.GOOGLE_CLIENT_EMAIL,
    client_id: process.env.GOOGLE_CLIENT_ID,
    auth_uri: 'https://accounts.google.com/o/oauth2/auth',
    token_uri: 'https://oauth2.googleapis.com/token',
    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.GOOGLE_CLIENT_EMAIL}`,
    universe_domain: 'googleapis.com',
  };

  const auth = new google.auth.GoogleAuth({
    credentials,
    scopes: ['https://www.googleapis.com/auth/drive.file'],
  });

  const clientAuth = await auth.getClient();

  const drive = google.drive({ version: 'v3', auth: clientAuth });
  const sheets = google.sheets({ version: 'v4', auth: clientAuth });

  return { drive, sheets };
}

async function grantPermission(fileId) {
  try {
    const { drive } = await getGoogleClient();
    await Promise.all([
      drive.permissions.create({
        fileId,
        requestBody: {
          type: 'domain',
          role: 'writer',
          domain: 'hiseven.com',
        },
      }),
      ...EXTERNAL_EMAILS.map((email) =>
        drive.permissions.create({
          fileId,
          requestBody: {
            type: 'user',
            role: 'writer',
            emailAddress: email,
          },
        }),
      ),
    ]);
  } catch (error) {
    console.error(
      '[ERROR] Failed to share the Google Sheet with HiSeven team or external users. ' +
        'Please check your Google API permissions and credentials. Details:',
      error,
    );
  }
}

async function getOrCreateFile() {
  const { drive, sheets } = await getGoogleClient();
  const res = await drive.files.list({
    q: `name='${FILE_NAME}' and trashed=false`,
    fields: 'files(id, name)',
    spaces: 'drive',
  });

  if (res.data.files && res.data.files.length > 0) {
    return res.data.files[0];
  }

  const resource = {
    name: FILE_NAME,
    mimeType: 'application/vnd.google-apps.spreadsheet',
    parents: ['root'],
  };

  const createResponse = await drive.files.create({
    resource,
    fields: 'id',
  });

  const spreadsheetId = createResponse.data.id;

  const createValueRequest = getUpdateSheetRequests(0);
  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: [
        {
          updateSheetProperties: {
            properties: {
              sheetId: 0,
              title: DEFAULT_BRANCH,
            },
            fields: 'title',
          },
        },
        ...createValueRequest,
      ],
    },
  });

  await updateJsonFile(spreadsheetId, 0);
  await grantPermission(createResponse.data.id);
}

async function getBranchname() {
  try {
    const name = execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
    // Google Sheets tab names cannot contain: [ ] : * ? / \ and must be <= 100 chars
    const safeName = name.replace(/[\[\]:*?/\\]/g, '_').slice(0, 100);
    return safeName;
  } catch (err) {
    return `chores/sys-${new Date().toISOString()}`;
  }
}

async function updateJsonFile(spreadsheetId, sheetId = 0) {
  const filePath = path.resolve(__dirname, '../src/dictionaries/zh.json');
  const zh = fs.readFileSync(filePath, 'utf-8');
  const zhData = JSON.parse(zh);

  const malaysiaTime = new Date().toLocaleString('en-US', {
    timeZone: 'Asia/Kuala_Lumpur',
  });
  const sheetUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit#gid=${sheetId}`;
  zhData.__SKIP_en_sheet_url = sheetUrl;
  zhData.__SKIP_en_last_sync = malaysiaTime;

  const json = JSON.stringify(zhData, null, 2).replace(
    /(\n)(\s*"__SKIP_en_page_)/g,
    '\n\n$2',
  );

  fs.writeFileSync(filePath, json, 'utf-8');
}

function getSheetValues() {
  const filePath = path.resolve(__dirname, '../src/dictionaries/zh.json');
  const zh = fs.readFileSync(filePath, 'utf-8');
  const zhData = JSON.parse(zh);

  const rows = [];
  const meta = [];

  for (const key of KEYS_TO_EXCLUDES) {
    delete zhData[key];
  }

  Object.entries(zhData).forEach(([key, value], index) => {
    if (index !== 0 && key.startsWith('__SKIP_en_page')) {
      rows.push(['', '']);
      meta.push({});
    }
    const formattedKey = key.endsWith(' ')
      ? key.replace(/ $/, '<end_space>')
      : key;
    const formattedValue =
      typeof value === 'string' && value.endsWith(' ')
        ? value.replace(/ $/, '<end_space>')
        : value;
    rows.push([formattedKey, formattedValue]);

    if (key.startsWith('__SKIP_en_page')) {
      meta.push({ yellow: true });
    } else {
      meta.push({});
    }
  });

  rows.unshift([]);
  const malaysiaTime = new Date().toLocaleString('en-US', {
    timeZone: 'Asia/Kuala_Lumpur',
  });
  rows.unshift(['__SKIP_en_last_sync', malaysiaTime]);
  meta.unshift([]);
  meta.unshift({ italic: true });

  rows._meta = meta;
  return rows;
}

function getUpdateSheetRequests(sheetId) {
  const rows = getSheetValues();

  const updateValuesRequest = {
    updateCells: {
      range: {
        sheetId,
        startRowIndex: 0,
        startColumnIndex: 0,
        endRowIndex: rows.length,
        endColumnIndex: 2,
      },
      rows: rows.map((row, idx) => {
        const meta = rows._meta[idx] || {};
        const userEnteredFormat = {
          wrapStrategy: 'WRAP',
          verticalAlignment: 'TOP',
        };

        if (meta.yellow) {
          userEnteredFormat.backgroundColor = { red: 1, green: 1, blue: 0 };
        }
        if (meta.italic) {
          userEnteredFormat.textFormat = { italic: true };
        }
        return {
          values: [
            {
              userEnteredValue: { stringValue: row[0] ?? '' },
              userEnteredFormat: Object.keys(userEnteredFormat).length
                ? userEnteredFormat
                : undefined,
            },
            {
              userEnteredValue: { stringValue: row[1] ?? '' },
              userEnteredFormat: Object.keys(userEnteredFormat).length
                ? userEnteredFormat
                : undefined,
            },
          ],
        };
      }),
      fields:
        'userEnteredValue,userEnteredFormat.backgroundColor,userEnteredFormat.textFormat.italic,userEnteredFormat.wrapStrategy,userEnteredFormat.verticalAlignment',
    },
  };

  const columnsRequests = COLUMN_WIDTHS.map((width, index) => ({
    updateDimensionProperties: {
      range: {
        sheetId,
        dimension: 'COLUMNS',
        startIndex: index,
        endIndex: index + 1,
      },
      properties: {
        pixelSize: width,
      },
      fields: 'pixelSize',
    },
  }));

  return [updateValuesRequest, ...columnsRequests];
}

async function pushToGoogleSheet() {
  if (
    !process.env.GOOGLE_PROJECT_ID ||
    !process.env.GOOGLE_PRIVATE_KEY_ID ||
    !process.env.GOOGLE_PRIVATE_KEY ||
    !process.env.GOOGLE_CLIENT_EMAIL ||
    !process.env.GOOGLE_CLIENT_ID
  ) {
    console.error(
      '[ERROR] Missing required environment variables for Google API.\n' +
        'Please check your .env file and make sure all Google credentials are set.\n' +
        'Required: GOOGLE_PROJECT_ID, GOOGLE_PRIVATE_KEY_ID, GOOGLE_PRIVATE_KEY, GOOGLE_CLIENT_EMAIL, GOOGLE_CLIENT_ID',
    );
    process.exit(1);
  }

  const getResponse = await getOrCreateFile();
  if (!getResponse) {
    console.log(
      '[INFO] No existing Google Sheet found. A new Google Sheet for translations has been created and synced.\n' +
        'You can now view or edit your translations in Google Sheets.',
    );
    process.exit(0);
  }

  const { id: spreadsheetId } = getResponse;
  const branch = await getBranchname();
  const { sheets } = await getGoogleClient();

  const sheetMeta = await sheets.spreadsheets.get({
    spreadsheetId,
  });
  const sheet = sheetMeta.data.sheets.find(
    (s) => s.properties.title === branch,
  );

  let sheetId = sheet?.properties.sheetId;
  if (typeof sheetId === 'undefined') {
    const addSheetResponse = await sheets.spreadsheets.batchUpdate({
      spreadsheetId,
      requestBody: {
        requests: [
          {
            addSheet: {
              properties: {
                title: branch,
              },
            },
          },
        ],
      },
    });

    sheetId = addSheetResponse.data.replies[0].addSheet.properties.sheetId;
    console.log(
      `[INFO] Created a new tab in Google Sheets for branch "${branch}".\n` +
        'Each branch has its own tab for translations.',
    );
  }

  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: getUpdateSheetRequests(sheetId),
    },
  });
  await updateJsonFile(spreadsheetId, sheetId);

  console.log(
    `[SUCCESS] Translation data has been synced to Google Sheets under the "${branch}" tab.\n` +
      'You can now review or update your translations in Google Sheets.',
  );
}

pushToGoogleSheet();
