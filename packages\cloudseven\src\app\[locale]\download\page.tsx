'use client';

import { useI18n } from '@hi7/lib/i18n';

import maskHomeLanding from '@hi7/assets/background/cloud.png';
import useScreenSize from '@hi7/helpers/useScreenSize';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import { Archivo } from 'next/font/google';
import Image from 'next/image';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

function Landing() {
  const locale = useLocale();
  const t = useI18n();
  const { isMobile } = useScreenSize();

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) overflow-hidden bg-[#B5E1FF] lg:min-h-(--min-h-hvh) lg:rounded-br-[100px] lg:rounded-bl-[100px] lg:pt-[78px]">
          <div className="absolute right-[-70%] bottom-0 left-[-70%] lg:right-0 lg:left-0">
            <div className="absolute bottom-0">
              <Image
                src={maskHomeLanding}
                alt={''}
                height={2160}
                width={3840}
              />
            </div>
          </div>

          <div className="relative h-screen flex flex-col items-center justify-center px-5 py-11 text-white lg:pt-[30px]">
            <div className="text-center lg:w-[60dvw] lg:min-w-[855px]">
              <h1 className="mb-[14px] flex flex-col text-[46px] leading-none lg:text-[64px]">
                <span className={clsx(locale === 'zh' && 'order-2')}>
                  <span className={clsx(
                    archivo.className,
                    'bg-gradient-to-r from-[#2243EA] via-[#33B2ED] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text text-[60px]',
                    'lg:inline-block lg:text-[78px] ',
                    'xl:text-[96px] ',
                    '2xl:text-[120px]'
                  )}>
                    {t('Get Free')}
                  </span>
                </span>
              </h1>
              <h3
                className={clsx(
                  'mb-[14px] text-[20px] font-[400] leading-[25.2px] text-black lg:whitespace-pre-lin',
                  locale === 'en' ? 'pb-[2dvh]' : 'py-[2dvh]',
                  'lg:text-[30px] lg:font-[400] lg:leading-[35px]',
                  'xl:text-[20px] xl:font-[400]',
                )}
              >
                {t(
                  'Secure cloud storage and access your files anywhere. Download Proton Drive for your device.',
                )}
              </h3>
            </div>
            <a
              className="justify-center rounded-xl bg-gradient-to-r from-[#2243EA] to-[#33B2ED] px-[20px] py-[10px] text-center text-[16px] font-[300] text-white hover:bg-[#2243EA]/80 lg:text-[24px] lg:font-[400] lg:py-5 lg:px-10 xl:text-[16px] xl:font-[50px] xl:py-3"
              href=""
              target="_blank"
            >
              {t('Windows Desktop')}
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Landing;
