import ChevronLeft from '@hi7/assets/icon/chevron-left.svg';
import ChevronRight from '@hi7/assets/icon/chevron-right.svg';
import { usePagination } from '@hi7/helpers/usePagination';
import useScreenSize from '@hi7/helpers/useScreenSize';
import { useI18n } from '@hi7/lib/i18n';
import clsx from 'clsx';

type PaginationProps = {
  totalPages: number;
};

const BUTTON_STYLES = {
  active: 'bg-[#E3FFFB] text-[#1E1E1E]',
  default:
    'text-[#000]/88 rounded-md px-3 py-1 transition-colors lg:hover:bg-gray-100 w-8 flex justify-center cursor-pointer',
};

const Pagination = ({ totalPages }: PaginationProps) => {
  const {
    currentPage,
    itemsPerPageOptions,
    selectedItemsPerPage,
    handlePageChange,
    handleItemsPerPageChange,
  } = usePagination();

  const { isMobile } = useScreenSize();
  const t = useI18n();

  const renderPageButtons = () => {
    const buttons = [];
    const VISIBLE_PAGE = isMobile ? 4 : 5;
    const firstPage = 1;
    const lastPage = totalPages;
    const totalDisplayPages = Math.max(totalPages, firstPage);

    if (totalDisplayPages <= VISIBLE_PAGE) {
      for (let index = firstPage; index <= totalDisplayPages; index++) {
        buttons.push(
          <button
            key={`randome-i1-${index}`}
            onClick={() => handlePageChange(index)}
            className={clsx(
              BUTTON_STYLES.default,
              currentPage === index && BUTTON_STYLES.active,
            )}
          >
            {index}
          </button>,
        );
      }
    } else {
      if (currentPage < VISIBLE_PAGE) {
        for (let index = firstPage; index <= VISIBLE_PAGE - 1; index++) {
          buttons.push(
            <button
              key={`randome-i2-${index}`}
              onClick={() => handlePageChange(index)}
              className={clsx(
                BUTTON_STYLES.default,
                currentPage === index && BUTTON_STYLES.active,
              )}
            >
              {index}
            </button>,
          );
        }
        buttons.push(
          <span key="ellipsis" className="px-2 py-1 text-[#D9D9D9]">
            ...
          </span>,
        );
        buttons.push(
          <button
            key={`randome-i3-${lastPage}`}
            onClick={() => handlePageChange(lastPage)}
            className={clsx(
              BUTTON_STYLES.default,
              currentPage === lastPage && BUTTON_STYLES.active,
            )}
          >
            {lastPage}
          </button>,
        );
      } else if (currentPage >= totalDisplayPages - VISIBLE_PAGE + 1) {
        buttons.push(
          <button
            key={`randome-i4-${firstPage}`}
            onClick={() => handlePageChange(firstPage)}
            className={clsx(
              BUTTON_STYLES.default,
              currentPage === firstPage && BUTTON_STYLES.active,
            )}
          >
            {firstPage}
          </button>,
        );
        buttons.push(
          <span key="ellipsis" className="px-2 py-1 text-[#D9D9D9]">
            ...
          </span>,
        );
        for (
          let index = totalDisplayPages - VISIBLE_PAGE + 1;
          index <= lastPage;
          index++
        ) {
          buttons.push(
            <button
              key={`randome-i5-${firstPage}`}
              onClick={() => handlePageChange(index)}
              className={clsx(
                BUTTON_STYLES.default,
                currentPage === index && BUTTON_STYLES.active,
              )}
            >
              {index}
            </button>,
          );
        }
      } else {
        buttons.push(
          <button
            key={`randome-i6-${firstPage}`}
            onClick={() => handlePageChange(firstPage)}
            className={clsx(
              BUTTON_STYLES.default,
              currentPage === firstPage && BUTTON_STYLES.active,
            )}
          >
            {firstPage}
          </button>,
        );
        buttons.push(
          <span key="ellipsis" className="px-2 py-1 text-[#D9D9D9]">
            ...
          </span>,
        );

        const start = Math.max(currentPage - 1, firstPage + 1);
        const end = Math.min(currentPage + 1, lastPage - 1);

        for (let index = start; index <= end; index++) {
          buttons.push(
            <button
              key={`randome-i7-${index}`}
              onClick={() => handlePageChange(index)}
              className={clsx(
                BUTTON_STYLES.default,
                currentPage === index && BUTTON_STYLES.active,
              )}
            >
              {index}
            </button>,
          );
        }

        buttons.push(
          <span key="ellipsis-end" className="px-2 py-1 text-[#D9D9D9]">
            ...
          </span>,
        );
        buttons.push(
          <button
            key={`randome-i8-${lastPage}`}
            onClick={() => handlePageChange(lastPage)}
            className={clsx(
              BUTTON_STYLES.default,
              currentPage === lastPage && BUTTON_STYLES.active,
            )}
          >
            {lastPage}
          </button>,
        );
      }
    }

    return buttons;
  };

  return (
    <div className="flex w-full items-center justify-center space-x-1 text-sm">
      <button
        className={clsx(
          'rounded-md p-2 hover:bg-gray-100 disabled:opacity-50',
          { 'pointer-events-none opacity-50': currentPage === 1 },
        )}
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        <ChevronLeft className="h-5 w-5" />
      </button>

      <div className="flex items-center space-x-1">{renderPageButtons()}</div>

      <button
        className={clsx('rounded-md p-2 hover:bg-gray-100', {
          'pointer-events-none opacity-50': currentPage === totalPages,
        })}
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        <ChevronRight className="h-5 w-5" />
      </button>

      <select
        value={selectedItemsPerPage}
        onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
        className="ml-1 items-center space-x-1 rounded-md border border-[#D9D9D9] px-2 py-1.5 hover:bg-gray-50 lg:flex"
      >
        {itemsPerPageOptions.map((option) => (
          <option
            key={`random-i9-${option}`}
            value={option}
            className="text-sm"
          >
            {option} / {t('page')}
          </option>
        ))}
      </select>
    </div>
  );
};

export default Pagination;
