'use client';

import newsImage1 from '@hi7/assets/background/news-1.png';
import Clock from '@hi7/assets/icon/clock.svg';
import Search from '@hi7/assets/icon/search.svg';
import LinkButton from '@hi7/components/Form/LinkButton';
import { useI18n } from '@hi7/lib/i18n';
import clsx from 'clsx';
import Image from 'next/image';
import { useState } from 'react';
import { F_CATEGORIES, F_NEWS } from './config';
import Pagination from './Pagination';

function page() {
  const [index, setIndex] = useState(0);
  const [cat, setCat] = useState(F_CATEGORIES[0]);
  const t = useI18n();

  return (
    <div className="flex items-center justify-center">
      <div className="w-full">
        <div className="relative pt-[52px] pb-[76px] text-[#002035] lg:pt-[78px] lg:pb-[100px]">
          <div className="relative px-4 lg:px-30">
            <div className="mb-[36px] text-[18px] lg:mb-[40px]">
              <div className="flex flex-col gap-[36px] lg:flex-row lg:gap-0">
                <h1 className="flex-1 text-center text-[36px] leading-[1.2] lg:mb-[16px] lg:text-left lg:text-[64px] lg:leading-none">
                  {t('Industry News')}
                </h1>

                <div className="grid h-[50px] w-full grid-cols-[24px_1fr] items-center gap-2 rounded-[30px] border border-[#D9D9D9] bg-white px-6 lg:mb-8 lg:max-w-[40dvw]">
                  <Search />
                  <input
                    className="text-lg text-[#000] outline-0"
                    placeholder={t('Search')}
                  />
                </div>
              </div>
            </div>
            <div className="mb-[30px] flex flex-col items-center gap-6 overflow-hidden rounded-[12px] bg-[#1A201B] text-white lg:flex-row">
              <div className="relative h-[220px] w-full lg:h-full lg:flex-[0.7]">
                <Image
                  src={newsImage1}
                  height={220}
                  width={800}
                  alt=""
                  className="lg:object-cover"
                />
              </div>
              <div className="flex flex-col gap-4 p-5 lg:flex-[0.3]">
                <div className="flex w-full flex-row items-center justify-center text-[14px]">
                  <div className="rounded-[4px] bg-[#007D93] px-2 py-1 text-[14px] text-white">
                    {t('Product News')}
                  </div>
                  <span className="flex-1 text-right">2025-04-01</span>
                </div>
                <h2 className="text-[28px] leading-[1.2] lg:text-[42px]">
                  哪个海外代理IP平台最值得推荐：排名前十强测评
                </h2>
                <p className="leading-[1.4]">
                  越来越多的人开始关注和使用海外代理IP服务。那么，哪个海外代理IP平台最值得推荐呢？本文将对排名前十的海外代理IP平台进行详细测评，帮助你找到最合适的选择。这里推荐使用Elfproxy海外代理IP
                </p>
                <LinkButton type="text" size="S" url="/industry-news/1">
                  {t('Read More')}
                </LinkButton>
              </div>
            </div>

            <div className="relative m-auto mb-[88px] grid w-[90px] grid-cols-5 gap-x-3">
              {Array(5)
                .fill(0)
                .map((_, idx) => (
                  <div
                    key={`random1-${idx}`}
                    className={clsx([
                      'h-[10px] w-[10px] cursor-pointer rounded-full transition-all duration-300 ease-in-out',
                      index === idx ? 'bg-[#31F4A0]' : 'bg-[#D9D9D9]',
                    ])}
                    onMouseEnter={() => setIndex(idx)}
                  ></div>
                ))}
            </div>
            <div className="no-scrollbar mb-6 w-full overflow-x-auto">
              <div className="flex h-[40px] flex-row flex-nowrap gap-2.5 text-[14px] whitespace-nowrap">
                {F_CATEGORIES.map((category, idx) => (
                  <div
                    key={`random2-${idx}`}
                    className={clsx(
                      'cursor-pointer rounded-[8px] p-4 py-2',
                      cat === category && 'bg-[#E3FFFB]',
                    )}
                    onClick={() => setCat(category)}
                  >
                    {t(category)}
                  </div>
                ))}
              </div>
            </div>

            <div className="mb-[70px] grid gap-x-[60px] gap-y-[40px] lg:grid-cols-3 lg:gap-y-[34px]">
              {F_NEWS.map((item, idx) => (
                <div
                  key={`random3-${idx}`}
                  className="mb-2.5 flex flex-col items-start gap-2.5"
                >
                  <Image
                    src={item.image}
                    alt=""
                    width={360}
                    height={240}
                    className="mb-4 h-[200px] w-full rounded-[12px] object-cover"
                  />
                  <div className="rounded-[4px] bg-[#007D93] px-2 py-1 text-[14px] text-white">
                    {t(item.category)}
                  </div>
                  <b className="text-[24px] leading-[1.2]">{item.title}</b>
                  <p className="leading-[1.4] text-[#1E1E1E]/65">{item.desc}</p>
                  <div className="flex w-full flex-row items-center pb-2.5 text-[14px] leading-[1.4] text-[#8C8C8C]">
                    <p className="flex-1">{item.createdAt}</p>
                    <p className="flex flex-row items-center gap-2 self-end">
                      <Clock />
                      {item.readTime}
                      {t(' min read')}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <Pagination totalPages={50} />
          </div>
        </div>
      </div>
    </div>
  );
}

export default page;
