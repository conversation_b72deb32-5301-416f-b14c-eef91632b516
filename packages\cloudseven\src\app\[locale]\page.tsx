'use client';

import Landing from '@hi7/components/Home/Landing';
import Section2 from '@hi7/components/Home/Section2';
import ChallengesSection from '@hi7/components/Home/Section3';
import Section4 from '@hi7/components/Home/Section4';
import Trusted from '@hi7/components/Home/Trusted';
import ScrollSnapContainer from '@hi7/components/ScrollSnapContainer';

export default function Page() {
  // Temporarily use test component to verify scroll snap
  // return <ScrollSnapTest />;

  return (
    <ScrollSnapContainer>
      <Landing />
      <Section2 />
      <ChallengesSection />
      <Section4 />
      <Empowering />
      <Trusted />
      <FooterBanner
        subtitle="Try Global Remote Work Monitoring for Free"
        title="EMPOWERING YOUR BUSINESS SUCCESS"
        highlightText="Limited Offer: Free trial-Cloud Office Monitoring + Cloud Desktop"
        ctaText="Try For Free"
      />
    </ScrollSnapContainer>
  );
}
