
import Feature1 from '@hi7/assets/icon/price-2-1.svg';
import Feature2 from '@hi7/assets/icon/price-2-2.svg';
import Feature3 from '@hi7/assets/icon/price-2-3.svg';
import Feature4 from '@hi7/assets/icon/price-2-4.svg';
import Feature5 from '@hi7/assets/icon/price-2-5.svg';
import Feature6 from '@hi7/assets/icon/price-2-6.svg';
import Feature7 from '@hi7/assets/icon/price-2-7.svg';
import Feature8 from '@hi7/assets/icon/price-2-8.svg';

export const PLANS = [
  {
    name: 'Cloud Office Monitoring',
    packages: [
      {
        name: 'Free Trial',
        price: {
          amount: 'FREE 15-day Standard Plan Trial',
          unit: '',
        },
        url: '',
        buttonLabel: "Try Now",
        features: [
          '',
          '',
        ],
        highlight: false,
        availableFeatures: [],
      },
      {
        name: 'Basic',
        price: {
          amount: '$7',
          unit: '/seat/mo',
        },
        url: '',
        buttonLabel: "Buy Now",
        features: [
          '$18/seat/3mo',
          '$70/seat/year',
        ],
        highlight: false,
        availableFeatures: [
          "Employee Activity Overview",
          "Device Resource Overview",
          "Device Management",
          "Employee Overview",
          "Employee Management",
          "Office Area Management",
          "Application Monitoring",
          "Web Monitoring",
        ],
      },
      {
        name: 'Standard',
        price: {
          amount: '$12',
          unit: '/seat/mo',
        },
        url: '',
        buttonLabel: "Buy Now",
        features: [
          '$30/seat/3mo',
          '$120/seat/year',
        ],
        highlight: true,
        availableFeatures: [
          "Employee Activity Overview",
          "Device Resource Overview",
          "Device Management",
          "Employee Overview",
          "Employee Management",
          "Office Area Management",
          "Application Monitoring",
          "Web Monitoring",
          "Application Policy",
          "Web Policy",
          "Network Policy",
          "Watermark Policy",
        ],
      },
      {
        name: 'Premium',
        price: {
          amount: '$XX',
          unit: '/seat/mo',
        },
        url: '',
        buttonLabel: "Contact Now",
        features: [
          '$XX/seat/3mo',
          '$XX/seat/year',
        ],
        highlight: false,
        availableFeatures: [
          "Employee Activity Overview",
          "Device Resource Overview",
          "Device Management",
          "Employee Overview",
          "Employee Management",
          "Office Area Management",
          "Application Monitoring",
          "Web Monitoring",
          "Application Policy",
          "Web Policy",
          "Network Policy",
          "Watermark Policy",
        ],
      },
      {
        name: 'Custom',
        price: {
          amount: '',
          unit: '',
        },
        url: '',
        buttonLabel: "Contact Now",
        features: [
          'Supports seft-hosted deployment on enterprise-owned servers',
          '\nComprehensive package for enhanced security and reliability',
        ],
        highlight: false,
        availableFeatures: [],
      },
    ],
  },
  {
    name: 'Cloud Desktop',
    packages: [
      {
        name: 'Free Trial',
        price: {
          amount: 'Free',
          unit: '',
        },
        url: '',
        buttonLabel: "Try Now",
        features: [
          'for New Users',
        ],
        highlight: false,
        availableFeatures: [],
      },
      {
        name: '4-Core CPU 8GB RAM / 40GB System Disk',
        price: {
          amount: '$7.50',
          unit: '',
        },
        url: '',
        buttonLabel: "Buy Now",
        features: [
          'Suitable for office work, international business operations, etc.',
        ],
        highlight: false,
        availableFeatures: [],
      },
      {
        name: '8-Core CPU 8GB RAM / 40GB System Disk',
        price: {
          amount: '$42.30',
          unit: '',
        },
        url: '',
        buttonLabel: "Buy Now",
        features: [
          'More stable performance, ideal for complex office environments.',
        ],
        highlight: false,
        availableFeatures: [],
      },
      {
        name: '8-Core CPU 8GB RAM / 40GB System Disk',
        price: {
          amount: '$57.43',
          unit: '',
        },
        url: '',
        buttonLabel: "Buy Now",
        features: [
          'Designed for software development, testing and professional use.',
        ],
        highlight: false,
        availableFeatures: [],
      },
    ],
  }
] as const;

export const FEATURES = [
  {
    Icon: Feature1,
    title: '200+ Countries /Regions',
    desc: 'Access real residential user devices globally, with the ability to select by country, state, or city.',
  },
  {
    Icon: Feature2,
    title: '99.99% Uptime',
    desc: 'Stable network operation, ensuring the highest success rate at the fastest speed.',
  },
  {
    Icon: Feature3,
    title: '3M+ Accounts',
    desc: 'Social media accounts running stably.',
  },
  {
    Icon: Feature4,
    title: '500+ Key Metrics',
    desc: 'Key business metrics monitored in real time.',
  },
  {
    Icon: Feature5,
    title: 'Safe and Secure',
    desc: "Whether it's social media account management or data collection, we help you get it done quickly.",
  },
  {
    Icon: Feature6,
    title: '24/7 Technical Support',
    desc: 'Experienced customer service and technical R&D teams provide 24/7 support for various business scenarios.',
  },
  {
    Icon: Feature7,
    title: 'Lifetime Traffic Validity',
    desc: 'Unlimited data usage with an outbound speed starting at 1000M, ensuring smooth performance!',
  },
  {
    Icon: Feature8,
    title: 'Affordable',
    desc: 'Average IP prices are 30-50% cheaper than similar products.',
  },
];

export const ALLBENEFITS = [
  {
    name: "Employee Activity Overview",
    description: "Track employee activity status & duration",
  },
  {
    name: "Device Resource Overview",
    description: "Monitor device usage and performance",
  },
  {
    name: "Device Management",
    description: "View and manage device performance",
  },
  {
    name: "Employee Overview",
    description: "Analyse desktop application usage & activity",
  },
  {
    name: "Employee Management",
    description: "Manage employees and assign seats",
  },
  {
    name: "Office Area Management",
    description: "Support LAN setup and intranet deployment",
  },
  {
    name: "Application Monitoring",
    description: "Track application resource consumption",
  },
  {
    name: "Web Monitoring",
    description: "View employees' web browsing history",
  },
  {
    name: "Application Policy",
    description: "Restrict the use of specific applications",
  },
  {
    name: "Web Policy",
    description: "Block specific domains and restrict web access",
  },
  {
    name: "Network Policy",
    description: "Auto-disconnect abnormal traffic to protect security",
  },
  {
    name: "Watermark Policy",
    description: "Add screen watermarks to prevent photo/screenshot leaks",
  },
]