import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'CloudSeven - Static IP/Dynamic IP/Native IP - Global Pure Residential IP Network',
    ),
    description: t(params.locale)(
      'CloudSeven has a global network of pure residential IPs in over 200 countries - providing dynamic IP pools, dynamic and static IPs, native residential IPs, HTTP(s) proxies, Socks5 proxies, and more. IP quality is guaranteed, with triple compensation in case of any issues! High anonymity dedicated IPs meet the needs of data scraping, cross-border e-commerce, social media operation, and more.',
    ),
    keywords: t(params.locale)(
      'CloudSeven, overseas IP, dynamic IP, static IP, static residential proxy, static residential IP, native IP',
    ),
  };
};

export default function PricingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
