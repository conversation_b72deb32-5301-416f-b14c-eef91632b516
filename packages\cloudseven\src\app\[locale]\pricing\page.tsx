'use client';

import CheckIcon from '@hi7/assets/icon/check-icon.svg';
import ChevronDown from '@hi7/assets/icon/chevron-down.svg';
import CloseButton from '@hi7/assets/icon/close-button.svg';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import { useState } from 'react';
import Modal from 'react-modal';
import { ALLBENEFITS, PLANS } from './config';

const customModalStyles = {
  overlay: {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(42, 41, 41, 0.75)'
  },
  content: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    right: 'auto',
    bottom: 'auto',
    width: '90%',
    height: '80%',
    marginRight: '-50%',
    transform: 'translate(-50%, -50%)',
    borderRadius: '10px',
    border: 'none',
    background: "#FFFFFF",
    padding: '0px',
    zIndex: 999,
  },
};
function Pricing() {
  const [selected, setSelected] = useState(0);
  const { packages } = PLANS[selected];
  const trimmedPackages = PLANS[selected]?.packages.slice(1, -1);
  const benefits = ALLBENEFITS;
  const t = useI18n();
  const locale = useLocale();
  const [modalIsOpen, setIsOpen] = useState(false);


  function openModal() {
    setIsOpen(true);
  }

  function closeModal() {
    setIsOpen(false);
  }

  const scrollToComparison = () => {
    const element = document.getElementById('comparison');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) overflow-hidden bg-linear-[0deg,#B6E2FF_0.54%,#FFF] pb-[160px] lg:min-h-(--min-h-hvh) lg:pt-[78px] lg:pb-0">

          <div className="relative flex flex-col items-center justify-center px-5 py-11 lg:pt-[60px]">
            <div className="w-[100%] lg:w-[80dvw] lg:min-w-[855px] xl:w-[90dvw] xl:min-w-[1200px]">
              <h1 className="text-[#000] mb-5 px-10 text-center text-[32px] font-[600] leading-none lg:text-[64px]">
                {t('Choose Your Ideal Plan')}
              </h1>
              <h3
                className={clsx(
                  'text-[#000] text-center mb-5 xl:w-[60vw] xl:mx-auto',
                )}
              >
                {t(
                  'Discover customisable plans designed to fit your unique needs. With flexible payment options, getting started has never been easier. Choose a service that aligns with your goals!',
                )}
              </h3>
              <div className="m-auto mb-[24px] flex flex-row justify-center gap-2.5 text-center lg:mb-[40px] lg:w-full xl:mb-25">
                {PLANS.map((plan, index) => (
                  <div
                    key={`plan-${plan.name}`}
                    className={clsx(
                      'cursor-pointer rounded-lg border border-[#3D5AEC] px-3 py-2 text-[14px] leading-[1.4] font-[500] transition-all lg:w-[250px] lg:px-6 lg:py-4 lg:text-[16px] hover:bg-[#3D5AEC] hover:text-white',
                      selected === index
                        ? 'bg-[#3D5AEC] text-white'
                        : 'bg-transparent text-[#3D5AEC]',
                    )}
                    onClick={() => setSelected(index)}
                  >
                    {t(plan.name)}
                  </div>
                ))}
              </div>
              <div className="flex flex-col justify-center gap-5 lg:flex-row lg:flex-wrap xl:flex-nowrap">
                {packages.map((pkg, idx) => (
                  <>
                    {pkg.highlight && (
                      <div className="relative lg:hidden">
                        <div className="absolute -top-7 left-1/2 transform -translate-x-1/2 translate-y-7 w-full">
                          <div className="bg-[#3D5AEC] text-white text-center px-3 py-1 rounded-xl text-[12px] font-[400] shadow-md pt-3 pb-7">
                            {t("Most Popular")}
                          </div>
                        </div>
                      </div>
                    )}
                    <div
                      key={`pkg1-${idx}`}
                      className={`relative flex flex-col gap-4 rounded-[15px] bg-white px-[20px] text-center py-[20px] text-[#1E1E1E] border-1 lg:w-[360px] xl:min-w-[190px] xl:h-[390px] xl:justify-between 2xl:h-[385px] ${pkg.highlight ? 'border-[#3D5AEC] mt-5 lg:px-0 xl:mt-0' : 'border-[#D9D9D9]'} `}
                    >
                      {/****** Most Popular Label ******/}
                      {pkg.highlight && (
                        <div className="hidden lg:block lg:absolute lg:-top-10 lg:left-1/2 lg:transform lg:-translate-x-1/2 lg:translate-y-5 lg:w-[101%] xl:translate-y-0 2xl:w-full">
                          <div className="bg-[#3D5AEC] text-white text-center px-3 py-1 rounded-xl text-[12px] font-[400] shadow-md pt-3 pb-7">
                            {t("Most Popular")}
                          </div>
                        </div>
                      )}
                      <div className={`relative flex flex-col gap-4 flex-grow 2xl:gap-0 ${pkg.highlight ? 'lg:bg-white rounded-t-xl xl:-translate-y-[20px] xl:pt-[20px]' : 'border-[#D9D9D9]'}`}>
                        <b className="text-[20px] font-[400] leading-[1.5] text-black xl:pt-[10%] 2xl:pt-5">
                          {t(pkg.name)}
                        </b>
                        <div className={`block -mb-6 md:-mb-4 lg:-mb-5 xl:mb-0 ${(idx === 4 ? 'xl:-mt-3' : `pt-2 ${PLANS[selected].name === 'Cloud Desktop' ? 'xl:-mb-10 2xl:mb-10' : 'xl:mb-10'}`)}`}>
                          {PLANS[selected].name === 'Cloud Desktop' && (
                            <span className="font-[400] text-[16px]">{idx !== 0 ? 'from' : ''}</span>
                          )}
                          <div className={`flex-1 mb-5 px-18 xl:px-0 ${(idx === 0 && pkg.price.amount === 'FREE 15-day Standard Plan Trial') ? 'text-[20px] 2xl:mb-12' : 'text-[42px] '}`}>
                            <b className={`leading-[1.2] font-[600]`}>
                              {pkg.price.amount}
                              <span className="text-[18px] leading-[1.4] font-normal">
                                {t(pkg.price.unit)}
                              </span>
                            </b>
                          </div>
                          <ul className="hidden flex flex-col gap-4 xl:block">
                            {pkg.features.map((feature, fIdx) => {
                              if (feature !== "") {
                                return (
                                  <li
                                    key={`pkg-${fIdx}`}
                                    className={`items-start gap-2.5 text-[16px] font-[400] leading-[1.2] ${(idx === 4 ? ' xl:leading-[1.3] xl:mb-4 2xl:mb-10 2xl:leading-[1.5]' : 'xl:leading-[1.6]')}`}
                                  >
                                    {t(feature)}
                                  </li>
                                )
                              }
                            }
                            )}
                          </ul>

                          <div className="flex flex-col gap-2.5 text-[16px] font-[400] leading-[1.2] mb-5 xl:hidden">
                            {
                              <span>
                                {pkg.features
                                  .map((feature, fIdx) => ({ feature, fIdx }))
                                  .filter(({ feature, fIdx }) => feature !== '' && idx !== 4)
                                  .map(({ feature }) => t(feature))
                                  .join(', ')
                                }

                                {idx === 4 &&
                                  <ul className="flex flex-col gap-4 font-[400] text-[14px]">
                                    {pkg.features.map((feature, fIdx) => {
                                      if (feature !== "") {
                                        return (
                                          <li
                                            key={`pkg-${fIdx}`}
                                            className=""
                                          >
                                            {t(feature)}
                                          </li>
                                        )
                                      }
                                    }
                                    )}
                                  </ul>
                                }
                              </span>
                            }
                          </div>
                        </div>
                        <a
                          href=''
                          target="_blank"
                          className="px-6 py-2 text-[14px] font-[500] rounded-md flex self-end justify-center border-1 w-fit mx-auto bg-[#FFFFFF] text-[#3D5AEC] hover:bg-[#3D5AEC] hover:text-white xl:w-fit"
                        >
                          {pkg.buttonLabel}
                        </a>
                      </div>
                    </div>
                  </>
                ))}
              </div>

              <div className="flex items-center justify-center pt-[40px] lg:pt-[84px] lg:mb-10">
                {PLANS[selected].name === 'Cloud Office Monitoring' && (
                  <>
                    <button onClick={openModal} className="flex rounded-lg border border-[#3D5AEC] px-4 py-2 text-[14px] font-[500] text-[#3D5AEC] hover:bg-[#3D5AEC] hover:text-white xl:hidden">{t("Compare Plan")} <ChevronDown className="ml-2" /></button>
                    <button onClick={scrollToComparison} className="hidden flex rounded-lg border border-[#3D5AEC] px-4 py-2 text-[14px] font-[500] text-[#3D5AEC] hover:bg-[#3D5AEC] hover:text-white xl:flex">{t("Compare Plan")} <ChevronDown className="ml-2" /></button>
                  </>
                )}
              </div>

              {/****** Comparison Table ******/}
              {PLANS[selected].name === 'Cloud Office Monitoring' && (
                <div id="comparison" className="hidden xl:block mt-10 2xl:w-[80%] mx-auto mb-30">
                  <h1 className="text-[#000] mt-30 mb-20 px-10 text-center text-[32px] font-[600] leading-none lg:text-[64px]">
                    {t('Compare Plan')}
                  </h1>
                  <div className="grid grid-cols-4 grid-cols-[34%_22%_22%_22%]">
                    {/****** Header row ******/}
                    <div className="col-span-1"></div>
                    {trimmedPackages.map((pkg, idx) => (
                      <div key={`header-${idx}`} className={`relative flex flex-col items-center justify-center text-center rounded-t-xl border ${pkg.highlight ? 'bg-[#F3F9FF] border-[#3D5AEC]' : 'bg-[#FFFFFF] border-[#D9D9D9] p-4'}`}>
                        {/****** Most Popular Label ******/}
                        {pkg.highlight && (
                          <div className="absolute top-0 w-[101%] transform -translate-y-10 bg-[#3D5AEC] text-white text-center px-3 pt-3 pb-7 rounded-t-xl text-[12px] font-[400] shadow-md border-[#3D5AEC] ">
                            {t("Most Popular")}
                          </div>
                        )}
                        <div className={`-translate-y-4 ${pkg.highlight ? 'bg-[#F3F9FF] w-full z-1 rounded-t-xl' : ''}`}>
                          <h3 className="font-[400] text-[24px] pt-5">{pkg.name}</h3>
                          <div className="mt-2">
                            <span className="text-[42px] font-[600]">{pkg.price.amount}</span>
                            <span className="text-[16px] font-[400]">{pkg.price.unit}</span>
                            <ul className="flex flex-col gap-4 xl:block">
                              {pkg.features.map((feature, fIdx) => {
                                if (feature !== "") {
                                  return (
                                    <li
                                      key={`pkg-${fIdx}`}
                                      className={`items-start gap-2.5 text-[16px] font-[400] leading-[1.2] ${(idx === 4 ? ' xl:leading-[1.3] xl:mb-4 2xl:mb-10 2xl:leading-[1.5]' : 'xl:leading-[1.6]')}`}
                                    >
                                      {t(feature)}
                                    </li>
                                  )
                                }
                              }
                              )}
                            </ul>
                          </div>
                        </div>

                        <a
                          href=''
                          className={`my-4 inline-block px-4 py-2 text-[14px] font-[500] rounded-md bg-[#FFFFFF] border-1 text-[#3D5AEC] hover:bg-[#3D5AEC] hover:text-white`}
                        >
                          {pkg.buttonLabel}
                        </a>
                      </div>
                    ))}
                  </div>

                  {/****** Features table ******/}
                  <div className="border border-[#D9D9D9] overflow-hidden">
                    {ALLBENEFITS.map((benefit, benefitIdx) => (
                      <div key={`benefit-${benefitIdx}`} className="border-b border-b-2 border-[#D9D9D9] last:border-b-1">
                        <div className="grid grid-cols-4 grid-cols-[34%_22%_22%_22%]">
                          {/****** Benefit name and description ******/}
                          <div className="col-span-1 px-5 py-2 bg-[#FFFFFF] border-r border-[#D9D9D9]">
                            <h4 className="font-[400] text-[16px]">{benefit.name}</h4>
                            <p className="font-[400] text-[14px] text-[#7E7E7E]">{benefit.description}</p>
                          </div>

                          {/****** Feature availability for eac plan ******/}
                          {trimmedPackages.map((pkg, pkgIdx) => (
                            <div key={`feature-${benefitIdx}-${pkgIdx}`} className={` p-4 flex items-center justify-center ${pkg.highlight ? 'bg-[#F3F9FF] text-white border border-l-[#3D5AEC] border-r-[#3D5AEC]' : 'bg-[#FFFFFF]'}`}>
                              {pkg.availableFeatures.includes(benefit.name) ? (
                                <CheckIcon className="text-green-500 w-6 h-6" />
                              ) : (
                                <span className="text-gray-300">—</span>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>


      {/****** Modal UI ******/}
      <Modal
        ariaHideApp={false}
        isOpen={modalIsOpen}
        onRequestClose={closeModal}
        style={customModalStyles}
        contentLabel="Plan Comparison Modal"
        className=""
      >
        <CloseButton className="scale-70 my-5 ml-5" onClick={closeModal} />

        <div className="flex flex-col items-center px-0 lg:p-8">
          <div className="flex flex-row overflow-x-auto w-full">
            {trimmedPackages.map((plan, idx) => (
              <div key={idx} className="relative min-w-[300px]">
                {/****** Most Popular Label ******/}
                {plan.highlight && (
                  <div className="absolute top-2 left-1/2 transform -translate-x-1/2 bg-[#3D5AEC] text-white text-center px-3 pt-3 pb-5 rounded-t-xl text-[12px] font-[400] shadow-md w-full z-[-1]">
                    {t("Most Popular")}
                  </div>
                )}
                <div
                  className={`border flex flex-col mt-12 rounded-t-xl ${plan.highlight ? 'border-[#3D5AEC] bg-[#F3F9FF] ' : 'border-[#D9D9D9] bg-white'}`}
                >
                  <div className={`flex flex-col items-center p-4 ${plan.highlight ? 'rounded-t-xl' : ''} `}>
                    <h3 className="text-lg font-semibold mb-2">{plan.name}</h3>
                    <div className="text-[42px] font-[600] mb-1">
                      {plan.price.amount}
                      <span className="text-[16px] font-[400]">/seat/mo</span>
                    </div>
                    <div className="text-[16px] font-[400] mb-3">
                      {plan.features.map((sp, i) => (
                        <div key={i}>{sp}</div>
                      ))}
                    </div>
                    <button className="bg-white text-[#3D5AEC] border border-[#3D5AEC] rounded-md px-4 py-2 text-sm hover:bg-[#3D5AEC] hover:text-white transition mb-3">
                      {plan.buttonLabel}
                    </button>
                  </div>

                  {/******  Benefits Section ******/}
                  <div className="border-t border-t-2 border-[#D9D9D9]">
                    {benefits.map((benefit, fIdx) => {
                      const isAvailable = plan.availableFeatures.includes(benefit.name);
                      return (
                        <div
                          key={fIdx}
                          className="flex items-center gap-2 px-4 py-2 text-sm border-b border-b-2 border-[#D9D9D9] last:border-b-0"
                        >
                          {isAvailable ? (
                            <CheckIcon className="flex-shrink-0" />
                          ) : (
                            <div className="h-4 w-4 flex-shrink-0 text-gray-300 my-[3px] mr-[3px]">—</div>
                          )}
                          <span className={isAvailable ? "text-gray-800" : "text-gray-400"}>
                            {benefit.name}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default Pricing;
