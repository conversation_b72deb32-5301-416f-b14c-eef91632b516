'use client';

import CircuitElement from '@hi7/assets/background/circuit-element.png';
import CogCloud from '@hi7/assets/icon/cog-cloud-icon.svg';
import CogSpanar from '@hi7/assets/icon/cog-spanar-icon.svg';
import HandheldClock from '@hi7/assets/icon/handheld-clock-icon.svg';
import ShieldLock from '@hi7/assets/icon/shield-lock-icon.svg';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import { Archivo } from 'next/font/google';
import Image from 'next/image';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});
function CDSection2() {

  const t = useI18n();
  const locale = useLocale();

  const features = [
    {
      title: 'High Anonymity & Security',
      description: 'Email registration with no verification required. Supports USDT payments and multiple international credit cards. Ensures business privacy and data security',
      icon: ShieldLock,
    },
    {
      title: 'Stable & Convenient Usage',
      description: '24/7 online stability. Instant access via web browser. One-click sharing for seamless collaboration',
      icon: HandheldClock,
    },
    {
      title: 'Flexible Configuration Options',
      description: 'Customise cloud desktop resources to match different business needs. Optimise workloads while reducing hardware costs and maintenance expenses',
      icon: CogSpanar,
    },
    {
      title: 'Extended Cloud Desktop Management',
      description: 'IT teams can centrally manage virtual desktops, including software installation, updates, security protection, and user activity monitoring. Enhances employee efficiency and simplifies remote IT management',
      icon: CogCloud,
    },
  ]

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) overflow-hidden bg-linear-[0deg,#B5E1FF_0.54%,#fff] pb-[50px] md:content-center md:pb-0 lg:h-(--min-h-hvh) lg:pt-[78px] lg:pb-0 xl:min-h-[105vh]">
          <div className="relative flex flex-col items-center justify-center px-5 py-11 lg:pt-[60px] xl:pt-[20px]">
            <div className="w-[100%] lg:w-[80dvw] lg:min-w-[855px] xl:w-[90dvw] xl:min-w-[1200px]">
              <h3 className="text-[#000] mb-5 px-10 text-center text-[28px] font-[600] leading-none lg:text-[48px]">
                {t('Deploy Fast for')}
              </h3>
              <h1 className="mb-[14px] flex flex-col leading-none mx-auto text-center">
                <span className={clsx(locale === 'zh' && 'order-2', 'self-center')}>
                  <span className={clsx(
                    archivo.className,
                    'bg-gradient-to-r from-[#3D5AEC] via-[#3D5AEC] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text text-[42px] uppercase',
                    'lg:inline-block lg:text-[78px] ',
                    'xl:text-[48px] ',
                    '2xl:text-[80px]'
                  )}>
                    {t('Instant Productivity')}
                  </span>
                </span>
              </h1>
              <Image src={CircuitElement} alt="Circuit Element" className="absolute right-0 top-0 opacity-70 lg:scale-150 lg:top-1/6 xl:scale-100 xl:w-[15%] xl:top-1/6" />
              <div className="flex flex-col md:grid md:grid-cols-2 md:justify-self-center md:gap-10 items-center justify-center gap-5 mt-10 xl:px-10 xl:flex xl:flex-row xl:flex-nowrap">
                {features.map((feature, idx) => (
                  <>
                    <div
                      key={`pkg1-${idx}`}
                      className={clsx(
                        'min-h-[300px] h-full w-full mb-5 relative flex flex-col gap-4 rounded-[15px] bg-linear[135deg,#FFFFFF_0.54%,#FFFFFF] px-[20px] shadow-[0px_4px_24px_-1px_rgba(0,0,0,0.2)] text-center py-[20px] text-[#1E1E1E]',
                        'lg:w-[360px]',
                        'xl:min-w-[160px] xl:w-full xl:h-[380px] xl:justify-between',
                        '2xl:h-[385px] 2xl:w-[340px] 2xl:min-w-[340px] 2xl:justify-between'
                      )}
                    >
                      <feature.icon className="scale-80" />
                      <div className={`relative flex flex-col gap-4 flex-grow 2xl:gap-0`}>
                        <b className={`text-[20px] font-[600] leading-[1.2] text-[#3D5AEC] xl:pt-3 2xl:pt-5 text-left`}>
                          {t(feature.title)}
                        </b>
                        <div className={`${archivo.className} flex flex-col gap-2.5 text-[16px] font-[400] leading-[1.2] text-left xl:mt-3 2xl:mt-5`}>
                          {
                            <span>
                              {t(feature.description)}
                            </span>
                          }
                        </div>
                      </div>
                    </div>
                  </>
                ))}
              </div>
              <Image src={CircuitElement} alt="Circuit Element" className="invert brightness-0 grayscale absolute left-0 bottom-0 transform rotate-180 opacity-50" />
            </div>
          </div>
        </div>
      </div>
    </div >
  );
}

export default CDSection2;
