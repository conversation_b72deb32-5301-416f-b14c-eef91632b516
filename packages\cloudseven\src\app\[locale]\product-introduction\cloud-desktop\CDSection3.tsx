'use client';

import Feat1 from '@hi7/assets/background/cd-section-3-feat-1.png';
import Feat2 from '@hi7/assets/background/cd-section-3-feat-2.png';
import Feat3 from '@hi7/assets/background/cd-section-3-feat-3.png';
import CircuitElement from '@hi7/assets/background/circuit-element.png';
import LongCircuitElement from '@hi7/assets/background/long-circuit-element.png';
import AnimationFrame from '@hi7/components/AnimationFrame';
import TriggerAnimation from '@hi7/components/TriggerAnimation';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import useEmblaCarousel from 'embla-carousel-react';
import { Archivo } from 'next/font/google';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});
function CDSection3() {

  const t = useI18n();
  const locale = useLocale();
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: false, align: 'center' });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;

    const originalArrayList = emblaApi.scrollSnapList()
    const filteredArrayList = Array.from(new Set(originalArrayList));
    setScrollSnaps(filteredArrayList);
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  const scrollTo = useCallback((index: number) => {
    if (!emblaApi) return;
    emblaApi.scrollTo(index);
  }, [emblaApi]);

  const features = [
    {
      title: 'Global Cloud- Based Office',
      description: 'Access your cloud desktop anytime, anywhere, from multiple devices for a seamless and efficient work experience.',
      background: Feat1,
    },
    {
      title: 'Comprehensive Employee Monitoring',
      description: 'Track real-time employee activity with detailed monitoring data, enhancing management efficiency.',
      background: Feat2,
    },
    {
      title: 'Secure Data Protection',
      description: 'Centralised cloud storage with multi-layer encryption ensures no risk of data leaks.',
      background: Feat3,
    },
  ]

  return (
    <TriggerAnimation>
      <div className="flex items-center justify-center overflow-hidden">
        <div className="w-full">
          <div className="relative min-h-(--min-sm-h-hvh) overflow-hidden bg-[#B5E1FF] md:min-h-[60vh] lg:pt-0 lg:pb-0 xl:min-h-[100vh] xl:h-[110vh] xl:pt-[70px] 2xl:pt-[120px]">
            <div className="relative flex flex-col items-center justify-center px-5 py-11 md:py-0 lg:pt-[60px]">
              <div className="w-[100%] lg:w-[80dvw] lg:min-w-[855px] xl:w-[90dvw] xl:min-w-[1200px]">
                <AnimationFrame
                  variant="SlideUp45AtEase"
                  once={false}
                  className="flex flex-col items-center justify-center xl:-mb-3 2xl:mb-10"
                >
                  <h1 className=" mb-[14px] flex flex-col leading-none mx-auto text-center">
                    <span className={clsx(locale === 'zh' && 'order-2', 'self-center')}>
                      <span className={clsx(
                        archivo.className,
                        'bg-gradient-to-r from-[#3D5AEC] via-[#3D5AEC] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text text-[42px] uppercase',
                        'lg:inline-block lg:text-[78px] ',
                        'xl:text-[48px] ',
                        '2xl:text-[80px]'
                      )}>
                        {t('Core Features')}
                      </span>
                    </span>
                  </h1>
                  <h3 className="text-[#000] mb-5 px-10 text-center text-[28px] font-[600] leading-none lg:text-[48px]">
                    {t('of Cloud Desktop')}
                  </h3>
                </AnimationFrame>
                <AnimationFrame
                  variant="SlideUp"
                  once={false}
                  className="flex flex-col items-center justify-center xl:-mb-8"
                >
                  <Image src={CircuitElement} alt="Circuit Element" className="invert brightness-0 grayscale absolute right-0 top-0 opacity-70 lg:scale-150 lg:top-1/4 xl:scale-110 xl:right-10 xl:w-[20%] xl:top-1/4 2xl:right-0" />
                </AnimationFrame>
                <div className="hidden lg:flex flex-row justify-self-center md:gap-10 items-center justify-center gap-5 mt-10 lg:mt-13 lg:w-full xl:px-10 xl:flex xl:flex-row xl:flex-nowrap xl:mt-15 xl:w-screen xl:justify-evenly 2xl:gap-30 2xl:w-[85vw]">
                  {features.map((feature, idx) => (
                    <>
                      <div
                        key={`pkg1-${idx}`}
                        className={clsx(
                          'min-h-[300px] h-full w-full mb-5 relative flex flex-col gap-4 rounded-[10px] text-center py-[20px] text-white',
                          'lg:w-[360px] lg:h-[420px] lg:justify-between',
                          'xl:min-w-[170px] xl:w-[310px] xl:h-[350px] xl:justify-between',
                          '2xl:h-[425px] 2xl:w-[350px] 2xl:min-w-[340px] 2xl:justify-between'
                        )}
                      >
                        <Image src={feature.background} alt="Circuit Element" className="absolute top-0 left-0 w-full h-full opacity-70 rounded-[10px]" />
                        <div className={`rounded-[10px] absolute top-0 left-0 w-full h-full opacity-95 bg-gradient-to-b from-transparent via-[#002C77CC] to-[#002C77]`} />
                        <div className={`relative flex flex-col gap-4 2xl:gap-0 justify-start p-6 mt-[45%] xl:mt-[30%] 2xl:mt-[45%]`}>
                          <div className="flex-grow" />
                          <b className={`text-[24px] font-[600] leading-[1.2] xl:pt-3 2xl:pt-5 text-left`}>
                            {t(feature.title)}
                          </b>
                          <div className={`flex flex-col gap-2.5 text-[16px] font-[400] leading-[1.2] text-left xl:mt-3 2xl:mt-5`}>
                            {
                              <span>
                                {t(feature.description)}
                              </span>
                            }
                          </div>
                        </div>
                      </div>
                    </>
                  ))}
                </div>


                {/****** Card carousel for MOBILE ******/}
                <div className="embla overflow-hidden w-full mt-15 lg:hidden" ref={emblaRef}>
                  <div className="embla__container flex">
                    {features.map((feature, idx) => (
                      <div
                        key={`feature-${idx}`}
                        className="embla__slide flex-shrink-0 w-[320px] mx-2"
                      >
                        <div className="min-h-[400px] h-full w-full relative flex flex-col rounded-[20px] text-white overflow-hidden">
                          <Image
                            src={feature.background}
                            alt=""
                            fill
                            className="object-cover opacity-70"
                          />
                          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#002C77CC] to-[#002C77]" />
                          <div className="relative p-6 mt-auto">
                            <h3 className="text-[24px] font-[600] mb-2">{feature.title}</h3>
                            <p className="text-[16px] font-[400]">{feature.description}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex justify-center mt-8 gap-2 lg:hidden">
                  {scrollSnaps.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => scrollTo(index)}
                      className={`w-3 h-3 rounded-full transition-colors ${index === selectedIndex ? 'bg-[#3D5AEC]' : 'bg-gray-300'}`}
                    />
                  ))}
                </div>
                <Image src={LongCircuitElement} alt="Circuit Element" className="invert brightness-0 grayscale absolute left-0 -bottom-7 transform opacity-50 xl:-bottom-20" />
              </div>
            </div>
          </div>
        </div>
      </div >
    </TriggerAnimation>

  );
}

export default CDSection3;
