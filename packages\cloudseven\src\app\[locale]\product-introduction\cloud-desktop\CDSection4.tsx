'use client';

import Card1Img from '@hi7/assets/icon/cd-section4-card-1.jpg';
import Card2Img from '@hi7/assets/icon/cd-section4-card-2.jpg';
import Card3Img from '@hi7/assets/icon/cd-section4-card-3.jpg';
import Card4Img from '@hi7/assets/icon/cd-section4-card-4.jpg';
import { useI18n } from '@hi7/lib/i18n';
import clsx from 'clsx';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Archivo, Archivo_Black } from 'next/font/google';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

const archivoBlack = Archivo_Black({
  subsets: ['latin'],
  weight: ['400'],
  display: 'swap',
});

function CDSection4() {
  const containerRef = useRef<HTMLDivElement>(null);
  const t = useI18n();
  const [currentIndex, setCurrentIndex] = useState(0);

  const cardData = [
    {
      title: 'International Business Operations',
      description: 'Bypass geographic and network restrictions with secure cloud desktop access. Effortlessly conduct cross-border online marketing and customer acquisition',
      image: Card1Img
    },
    {
      title: 'Mobile Remote Work',
      description: 'Dedicated online collaboration workspace for distributed teams. Unified maintenance & security controls for enhanced business flexibility.',
      image: Card2Img
    },
    {
      title: 'Software Development & Testing',
      description: 'Standardised cloud development environments for remote and outsourced teams. Optimised resource allocation to improve team collaboration efficiency.',
      image: Card3Img
    },
    {
      title: 'Data Security & Compliance',
      description: 'High-security cloud storage prevents sensitive data from being stored on local devices. Reduces the risk of data breaches and unauthorised access',
      image: Card4Img
    },
  ];

  // Much smaller container height - only 50vh extra for scroll animation
  const containerHeight = `100vh`; // Fixed small height instead of scaling with cards

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start center", "end center"]
  });

  // Map scroll progress to card index
  const cardIndex = useTransform(
    scrollYProgress,
    [0.2, 0.8], // Animation happens only in middle 60% of scroll
    [0, cardData.length - 1]
  );

  useEffect(() => {
    const unsubscribe = cardIndex.on("change", (latest) => {
      const clampedIndex = Math.max(0, Math.min(cardData.length - 1, Math.round(latest)));
      setCurrentIndex(clampedIndex);
    });
    return () => unsubscribe();
  }, [cardIndex, cardData.length]);

  const stackVariants = {
    top: {
      y: 0,
      scale: 1,
      zIndex: 4,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 30 }
    },
    middle: {
      y: 60,
      scale: 0.96,
      zIndex: 3,
      opacity: 0.95,
      transition: { type: "spring", stiffness: 300, damping: 30 }
    },
    bottom: {
      y: 120,
      scale: 0.92,
      zIndex: 2,
      opacity: 0.8,
      transition: { type: "spring", stiffness: 300, damping: 30 }
    },
    hidden: {
      y: 180,
      scale: 0.88,
      zIndex: 1,
      opacity: 0.6,
      transition: { type: "spring", stiffness: 300, damping: 30 }
    }
  };

  const getStackPosition = (index: number) => {
    const relativeIndex = index - currentIndex;
    if (relativeIndex === 0) return 'top';
    if (relativeIndex === 1) return 'middle';
    if (relativeIndex === 2) return 'bottom';
    return 'hidden';
  };

  const scrollToCard = (index: number) => {
    if (!containerRef.current) return;

    const sectionTop = containerRef.current.offsetTop;
    const sectionHeight = containerRef.current.offsetHeight;
    const scrollToPosition = sectionTop + (index / (cardData.length - 1)) * (sectionHeight * 0.6);

    window.scrollTo({
      top: scrollToPosition,
      behavior: 'smooth'
    });
  };

  return (
    <div
      ref={containerRef}
      className="relative bg-gradient-to-b from-[#B5E1FF] to-white overflow-hidden"
      style={{ height: containerHeight, overflowY: 'scroll', position: 'sticky', top: '5%', left: 0, right: 0 }}
    >
      {/* Sticky content that stays in view while scrolling */}
      <div className="sticky top-0 h-screen flex items-center justify-center">
        <div className="w-full pt-[10%] pb-[20%] overflow-y-scroll">
          <h3 className="text-[#000] mb-5 px-10 text-center text-[28px] font-[600] leading-none lg:text-[48px]">
            {t('Areas to Support Your')}
          </h3>
          <h1 className="flex flex-col leading-none mx-auto text-center">
            <span className={clsx(
              archivo.className,
              'bg-gradient-to-r from-[#3D5AEC] via-[#3D5AEC] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text text-[42px] uppercase',
              'lg:inline-block lg:text-[78px] ',
              'xl:text-[48px]',
              '2xl:text-[80px]'
            )}>
              {t('Business Expansion')}
            </span>
          </h1>

          <div className="hidden xl:block">
            {/* Left side indicators */}
            <div className="absolute flex flex-col gap-10 left-[5%] top-1/2 transform -translate-y-1/2 2xl:left-[7%]">
              {cardData.map((_, index) => (
                <button
                  key={index}
                  onClick={() => scrollToCard(index)}
                  className={`${archivoBlack.className} w-full h-full transition-all pl-3 text-[24px] text-[#3D5AEC] 2xl:text-[32px] ${index === currentIndex
                    ? 'border-l-2 border-[#3D5AEC] font-bold'
                    : 'opacity-60'
                    }`}
                >
                  0{index + 1}
                </button>
              ))}
            </div>

            {/* Card stack container */}
            <div className="relative mx-auto w-[80%] h-full">
              {cardData.map((data, index) => (
                <motion.div
                  key={index}
                  variants={stackVariants}
                  animate={getStackPosition(index)}
                  initial="hidden"
                  className={clsx(
                    "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",
                    "w-[95%] min-h-[280px] h-[350px] rounded-xl",
                    "bg-[linear-gradient(90deg,_#FFFFFF,_#FFFFFF80)]",
                    "shadow-[0px_4px_24px_-1px_rgba(0,0,0,0.2)] p-6",
                    "xl:min-h-[350px] xl:h-[380px]",
                    "2xl:min-h-[400px] 2xl:h-[450px] 2xl:w-[88%]"
                  )}
                >
                  <div className="flex mx-auto px-10 gap-10">
                    <div className="flex-[2.3] grid content-center gap-5 w-full -ml-5">
                      <h2 className="text-4xl text-[#33B2ED] xl:leading-[50px] xl:text-[48px]">
                        {data.title}
                      </h2>
                      <p className="font-[400] text-[16px] text-black w-[80%] 2xl:w-[90%]">
                        {data.description}
                      </p>
                    </div>
                    <div className="flex-[2] py-5 justify-center items-center content-center">
                      <Image
                        src={data.image}
                        alt={`Card image ${index + 1}`}
                        className="w-full rounded-[10px] max-h-[400px] h-[350px] transform scale-130 translate-x-[7%] 2xl:translate-x-[15%] 2xl:scale-115"
                      />
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Mobile version */}
          <div className="xl:hidden px-4">
            <div className="space-y-6">
              {cardData.map((data, index) => (
                <div
                  key={index}
                  className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg p-6"
                >
                  <div className="flex flex-col gap-4">
                    <Image
                      src={data.image}
                      alt={`Card image ${index + 1}`}
                      className="w-full h-48 object-cover rounded-lg"
                    />
                    <h3 className="text-2xl text-[#33B2ED] font-semibold">
                      {data.title}
                    </h3>
                    <p className="text-gray-700">
                      {data.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CDSection4;