'use client';

import CircleElement from '@hi7/assets/background/circle-element1.png';
import CircleElement2 from '@hi7/assets/background/circle-element2.png';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import useEmblaCarousel from 'embla-carousel-react';
import { Archivo } from 'next/font/google';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});
function CDSection5() {

  const t = useI18n();
  const locale = useLocale();
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: false, align: 'center' });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;

    const originalArrayList = emblaApi.scrollSnapList()
    const filteredArrayList = Array.from(new Set(originalArrayList));
    setScrollSnaps(filteredArrayList);
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  const scrollTo = useCallback((index: number) => {
    if (!emblaApi) return;
    emblaApi.scrollTo(index);
  }, [emblaApi]);

  const features = [
    {
      comment: 'As a Web3 project, our team members and core community contributors are spread worldwide. To enhance collaboration under a distributed work model while ensuring data security, we chose CloudSeven’s cloud desktop solution. It has significantly improved our team’s efficiency!',
      position: 'Web3 Project in Dubai (Using Cloud Desktop for Distributed Team Collaboration)',
    },
    {
      comment: 'We are a China-based cross-border trading company. To expand our business globally and overcome geographic and policy restrictions, we used CloudSeven’s cloud desktop to create a stable online work environment in different regions. This has ensured the smooth operation of our online stores and social media marketing platforms worldwide.',
      position: 'Chinese Cross-Border Merchant (Using Cloud Desktop for Global E-Commerce & Marketing)',
    },
    {
      comment: 'We provide global marketing services for businesses expanding overseas. With Cloud Desktop, we create secure environments for clients to manage international social media accounts. It helps us avoid the chaos of managing multiple clients, regions, platforms, and accounts, while ensuring security across all platforms!',
      position: 'Overseas Marketing Service Provider (Using Cloud Desktop for Managed International Marketing Operations)',
    },
  ]

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) overflow-hidden bg-linear-[0deg,#FFF_0%,#B5E1FF_50%,#FFF] pb-[160px] pt-20 lg:min-h-(--min-h-hvh) lg:pt-0 lg:pb-0 xl:pt-[7%] xl:min-h-[100vh] xl:h-[110vh] 2xl:pt-[10%] 2xl:h-[105vh]">

          <div className="relative flex flex-col items-center justify-center px-5 py-11 md:py-0 lg:pt-[60px]">
            <div className="w-[100%] lg:w-[80dvw] lg:min-w-[855px] xl:w-[90dvw] xl:min-w-[1200px]">
              <h3 className="text-[#000] mb-5 text-center text-[28px] font-[600] leading-none lg:text-[48px]">
                {t('Hear What')} {` `}
                <br className="hidden lg:block" />
                <span className={clsx(
                  archivo.className,
                  'bg-gradient-to-r from-[#3D5AEC] via-[#3D5AEC] to-[#33B2ED] font-[900] text-transparent bg-clip-text text-[42px] uppercase',
                  'lg:inline-block lg:text-[58px] lg:pt-3',
                  'xl:text-[48px] ',
                  '2xl:text-[80px]'
                )}>
                  {t('Our Clients Say')}
                </span>
              </h3>
              <Image src={CircleElement} alt='circle element background' className="hidden xl:flex absolute right-0 top-0 opacity-100 " />
              <div className="hidden lg:flex flex-row justify-self-center md:gap-10 items-center justify-center gap-5 mt-10 lg:mt-13 lg:w-full xl:px-20 xl:flex xl:flex-row xl:flex-nowrap xl:mt-10 xl:w-screen xl:justify-evenly 2xl:gap-0 2xl:w-[85vw]">
                {features.map((feature, idx) => (
                  <>
                    <div
                      key={`pkg1-${idx}`}
                      className={clsx(
                        'z-1 min-h-[300px] h-full w-full mb-5 relative flex flex-col gap-4 rounded-[15px] text-center py-[20px] bg-gradient-to-b from-[#ffffff] to-[#ffffff]/50 px-[20px] shadow-[0px_4px_24px_-1px_rgba(0,0,0,0.2)]',
                        'lg:w-[360px] lg:w-full lg:min-h-[450px] lg:h-full lg:justify-between lg:px-[0px] lg:py-0',
                        'xl:min-w-[170px] xl:w-[340px] xl:min-h-[380px] xl:h-full xl:justify-center xl:items-center xl:py-0',
                        '2xl:h-[380px] 2xl:w-[400px] 2xl:min-w-[340px] 2xl:items-center 2xl:justify-center'
                      )}
                    >
                      <div className={`relative flex flex-col gap-4 2xl:gap-0 justify-start p-6 text-black`}>
                        <div className="flex-grow" />
                        <p className={`text-[14px] font-[400] leading-[1.2] text-left`}>
                          "{t(feature.comment)}"
                        </p>
                        <div className={`flex flex-col gap-2.5 text-[14px] font-[600] italic leading-[1.2] text-left xl:mt-3 2xl:mt-5`}>
                          {
                            <span>
                              {t(feature.position)}
                            </span>
                          }
                        </div>
                      </div>
                    </div>
                  </>
                ))}
              </div>


              {/****** Card carousel for MOBILE ******/}
              <div className="embla overflow-hidden w-full mt-5 lg:hidden" ref={emblaRef}>
                <div className="embla__container flex py-8 px-3">
                  {features.map((feature, idx) => (
                    <div
                      key={`feature-${idx}`}
                      className="embla__slide flex-shrink-0 w-[320px] mx-2 bg-gradient-to-b from-[#ffffff] to-[#ffffff]/50 shadow-[0px_4px_24px_-1px_rgba(0,0,0,0.2)] rounded-[20px] overflow-hidden"
                    >
                      <div className="min-h-[400px] h-full w-full relative flex flex-col rounded-[20px] text-black overflow-hidden">
                        <div className="relative px-8 py-10 my-auto">
                          <p className="text-[16px] font-[400] mb-2">{feature.comment}</p>
                          <br />
                          <p className="text-[16px] font-[600]">{feature.position}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-center mt-8 gap-2 lg:hidden">
                {scrollSnaps.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => scrollTo(index)}
                    className={`w-2 h-2 rounded-full transition-colors ${index === selectedIndex ? 'bg-[#3D5AEC]' : 'bg-gray-300'}`}
                  />
                ))}
              </div>
              <Image src={CircleElement2} alt="Circle Element" className="hidden xl:flex absolute left-[-10%] top-10 scale-80 opacity-100 z-[0]" />
            </div>
          </div>
        </div>
      </div>
    </div >
  );
}

export default CDSection5;
