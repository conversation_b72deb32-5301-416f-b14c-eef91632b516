'use client';

import { useI18n } from '@hi7/lib/i18n';

import CloudDesktopHeroBg2 from '@hi7/assets/background/cloud-desktop-hero-bg2.jpg';
import HeroImg from '@hi7/assets/background/cloud-desktop-hero-img.png';
import CloudStripElement from '@hi7/assets/background/cloud-strip-element.svg';
import useScreenSize from '@hi7/helpers/useScreenSize';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import { Archivo } from 'next/font/google';
import Image from 'next/image';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

// import LinkButton from '../Form/LinkButton';

function Hero() {
  const locale = useLocale();
  const t = useI18n();
  const { isMobile } = useScreenSize();

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) bg-white overflow-hidden bg-[#FFFFFF] lg:min-h-[70vh] lg:pt-[50px] xl:h-screen xl:pt-[0px] 2xl:h-screen flex items-center">
          <Image src={CloudDesktopHeroBg2} alt="Hero Background" className="absolute object-cover inset-0 bg-bottom-right w-full h-full opacity-70" />
          <div className='absolute bottom-0 left-0 w-full h-1/8 bg-gradient-to-t from-white to-transparent backdrop-blur-sm opacity-100' />

          <div className="container mx-auto px-4 flex flex-col lg:flex-row items-center justify-center relative z-10 py-16 lg:pt-[50px] lg:py-24">
            <div className="w-full lg:w-1/2 px-10 text-center mb-8 lg:text-left xl:mb-0 xl:mt-5 2xl:mb-8 2xl:mt-0 xl:w-5/9 2xl:w-4/7">
              <h1 className="mb-[14px] flex flex-col leading-none mx-auto">
                <span className={clsx(locale === 'zh' && 'order-2', 'self-center')}>
                  <span className={clsx(
                    archivo.className,
                    'bg-gradient-to-r from-[#2243EA] via-[#2243EA] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text text-[42px] uppercase',
                    'lg:inline-block lg:text-[78px] ',
                    'xl:text-[86px] ',
                    '2xl:text-[120px]'
                  )}>
                    {t('Cloud Desktop')}
                  </span>
                </span>
              </h1>
              <p className="text-[16px] font-[400] lg:text-xl mb-3 text-black">
                Providing a secure and private office environment to help businesses expand globally with ease
              </p>
              <button className="mb-5 bg-linear-[135deg,#2243EA_0%,#33B2ED] font-[500] text-[14px] text-white mx-auto items-center justify-center px-6 py-2 rounded-md font-medium hover:bg-opacity-90 transition">
                Try For Free
              </button>
              <CloudStripElement className="hidden lg:block lg:absolute lg:-bottom-35 lg:left-0 xl:scale-70 xl:-bottom-10 xl:-left-20 2xl:-left-45 2xl:bottom-0 2xl:scale-120" />
            </div>

            <div className="block w-1/2 mx-auto w-full xl:w-4/9 2xl:w-3/7">
              <Image src={HeroImg} alt="Hero Image" className="mx-auto scale-110 md:translate-x-8 lg:translate-x-0 xl:translate-y-20 xl:scale-110 2xl:mt-15 2xl:-mr-15 2xl:scale-125 2xl:translate-x-17" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Hero;
