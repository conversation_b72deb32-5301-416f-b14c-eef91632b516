'use client';


import FooterBanner from '@hi7/components/Footer/banner';
import PageWrapper from '@hi7/components/PageWrapper';
import Section2 from './CDSection2';
import Section3 from './CDSection3';
import Section4 from './CDSection4';
import Section5 from './CDSection5';
import <PERSON> from './Hero';

// import Hero from './Hero';
// import ProxyTemplate from '@hi7/app/[locale]/product-introduction/cloud-desktop/index';

export default async function Page() {

  const sectionConfigs = [
    { scrollDirection: 'slide-up' as const },
    { scrollDirection: 'slide-up' as const },
    { scrollDirection: 'slide-up' as const },
    { scrollDirection: 'slide-up' as const },
    { scrollDirection: 'default' as const },
    { scrollDirection: 'default' as const },
  ];


  return (
    <PageWrapper sectionConfigs={sectionConfigs}>
      <Hero />
      <Section2 />
      <Section3 />
      <Section4 />
      <Section5 />
      <FooterBanner
        subtitle="Start and Scale Your Global Business with"
        title="CLOUD DESKTOP"
        highlightText="Limited Offer: Free trial-Cloud Desktop"
        ctaText="Try For Free"
      />
    </PageWrapper>
  );
}
