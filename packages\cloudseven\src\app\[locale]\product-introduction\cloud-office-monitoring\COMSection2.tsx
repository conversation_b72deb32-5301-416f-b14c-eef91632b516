'use client';

import CircuitElement from '@hi7/assets/background/circuit-element.png';
import LongCircuitElement from '@hi7/assets/background/long-circuit-element.png';
import Deploy from '@hi7/assets/icon/deploy.svg';
import RealTimeMonitor from '@hi7/assets/icon/real-time-monitor.svg';
import SpanarScrewdriver from '@hi7/assets/icon/spanar-screwdriver.svg';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import { Archivo } from 'next/font/google';
import Image from 'next/image';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});
function COMSection2() {

  const t = useI18n();
  const locale = useLocale();

  const features = [
    {
      title: 'Technical Services',
      description: 'We provide 24/7 technical support and continuous product updates. After purchase, users will benefit from ongoing service iterations, ensuring their business always operates under optimal conditions.',
      icon: SpanarScrewdriver,
    },
    {
      title: 'Private Deployment',
      description: "All data can be deployed on the client's own servers, ensuring data security and privacy.",
      icon: Deploy,
    },
    {
      title: 'Real-Time Monitoring & Behaviour Analysis',
      description: 'Monitor user resources, application usage and web activity in real time while analysing employee engagement to optimise resource allocation & enhance productivity.',
      icon: RealTimeMonitor,
    },
  ]

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) overflow-hidden bg-linear-[0deg,#B5E1FF_0.54%,#fff] pb-[50px] md:content-center md:pb-0 lg:min-h-(--min-h-hvh) lg:h-full lg:pt-[78px] lg:pb-[40px] xl:min-h-[115vh] 2xl:min-h-screen">
          <div className="relative flex flex-col items-center justify-center px-5 py-11 lg:pt-[60px] xl:pt-[20px]">
            <div className="w-[100%] lg:w-[80dvw] lg:min-w-[855px] xl:w-[90dvw] xl:min-w-[1200px]">
              <h3 className="text-[#000] mb-5 px-10 text-center text-[28px] font-[600] leading-none lg:text-[48px]">
                {t('Instantly Achieve')}
              </h3>
              <h1 className="mb-[14px] flex flex-col leading-none mx-auto text-center">
                <span className={clsx(locale === 'zh' && 'order-2', 'self-center')}>
                  <span className={clsx(
                    archivo.className,
                    'bg-gradient-to-r from-[#3D5AEC] via-[#3D5AEC] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text text-[42px] uppercase',
                    'lg:inline-block lg:text-[78px] ',
                    'xl:text-[48px] ',
                    '2xl:text-[80px]'
                  )}>
                    {t('Efficient Management')}
                  </span>
                </span>
              </h1>
              <Image src={CircuitElement} alt="Circuit Element" className="absolute right-0 top-0 opacity-70 lg:scale-150 lg:top-1/6 xl:scale-100 xl:hidden" />
              <div className="flex flex-col md:grid md:grid-cols-2 md:justify-self-center md:gap-10 items-center justify-center gap-5 mt-10 xl:px-10 xl:flex xl:flex-row xl:flex-nowrap 2xl:gap-[70px]">
                {features.map((feature, idx) => (
                  <>
                    <div
                      key={`pkg1-${idx}`}
                      className={clsx(
                        'min-h-[320px] h-full w-full mb-5 relative flex flex-col gap-4 rounded-[15px] bg-[linear-gradient(135deg,_rgba(255,255,255,0.6)_0%,_rgba(255,255,255,0.1)_100%)] px-[20px] shadow-[0px_4px_24px_-1px_rgba(0,0,0,0.2)] text-center py-[20px] md:px-[30px] text-[#1E1E1E]',
                        'lg:w-[360px]',
                        'xl:min-w-[160px] xl:w-full xl:h-[360px] xl:justify-between',
                        '2xl:h-[330px] 2xl:w-[400px] 2xl:min-w-[340px] 2xl:justify-between'
                      )}
                    >
                      <feature.icon className="scale-80 " />
                      <div className={`relative flex flex-col gap-4 flex-grow 2xl:gap-0 mt-5 xl:mt-0`}>
                        <b className={`text-[24px] font-[600] leading-[1.2] text-[#3D5AEC] xl:pt-3 2xl:pt-5 text-left`}>
                          {t(feature.title)}
                        </b>
                        <div className={`flex flex-col gap-2.5 text-[16px] font-[400] leading-[1.4] tracking-normal text-left xl:mt-3 2xl:mt-5`}>
                          {
                            <span>
                              {t(feature.description)}
                            </span>
                          }
                        </div>
                      </div>
                    </div>
                  </>
                ))}
              </div>
              <Image src={LongCircuitElement} alt="Circuit Element" className="hidden lg:block invert brightness-0 grayscale absolute left-0 bottom-0 transform opacity-100 md:-bottom-10" />
            </div>
          </div>
        </div>
      </div>
    </div >
  );
}

export default COMSection2;
