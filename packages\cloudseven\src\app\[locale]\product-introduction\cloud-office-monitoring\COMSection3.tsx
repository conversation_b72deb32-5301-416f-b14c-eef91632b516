'use client';

import CircuitElement from '@hi7/assets/background/circuit-element.png';
import Feat1 from '@hi7/assets/background/com-section-3-feat-1.png';
import Feat2 from '@hi7/assets/background/com-section-3-feat-2.png';
import Feat3 from '@hi7/assets/background/com-section-3-feat-3.png';
import Feat4 from '@hi7/assets/background/com-section-3-feat-4.png';
import Feat5 from '@hi7/assets/background/com-section-3-feat-5.png';
import ThreeLineCircuitElement from '@hi7/assets/background/three-line-circuit-element.png';
import LeftArrow from '@hi7/assets/icon/left-arrow-icon.svg';
import RightArrow from '@hi7/assets/icon/right-arrow-icon.svg';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import useEmblaCarousel from 'embla-carousel-react';
import { Archivo } from 'next/font/google';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';


const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});
function COMSection3() {

  const t = useI18n();
  const locale = useLocale();
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: false, align: 'center' });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);
  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false);
  const [nextBtnEnabled, setNextBtnEnabled] = useState(true);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
    setPrevBtnEnabled(emblaApi.canScrollPrev());
    setNextBtnEnabled(emblaApi.canScrollNext());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;

    const originalArrayList = emblaApi.scrollSnapList()
    const filteredArrayList = Array.from(new Set(originalArrayList));
    setScrollSnaps(filteredArrayList);
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  const scrollTo = useCallback((index: number) => {
    if (!emblaApi) return;
    emblaApi.scrollTo(index);
  }, [emblaApi]);

  const features = [
    {
      title: 'Real-Time Desktop Monitoring',
      description: 'Monitor employee device resources, application usage and web activity in real time. Track work progress and achieve centralised supervision.',
      background: Feat1,
    },
    {
      title: 'Employee Behaviour Analysis',
      description: 'Analyse employee computer activity to optimise workload distribution and resource allocation.',
      background: Feat2,
    },
    {
      title: 'Data Leak Prevention',
      description: 'Control access to confidential files and sensitive data, preventing information leaks and ensuring enterprise data security.',
      background: Feat3,
    },
    {
      title: 'Report Generation',
      description: 'Generate comprehensive reports for an overview of employee activity, reducing management costs.',
      background: Feat4,
    },
    {
      title: 'Real-Time Alerts',
      description: 'Receive instant alerts for unauthorised activities, allowing administrators to take timely action and minimise potential losses.',
      background: Feat5,
    },
  ]

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) overflow-hidden bg-[#B5E1FF] md:min-h-[60vh] lg:pt-0 lg:pb-0 xl:min-h-[100vh]">

          <div className="relative flex flex-col items-center justify-center px-5 py-11 md:py-0 lg:pt-[70px]">
            <div className="w-[100%] lg:w-[80dvw] lg:min-w-[855px] xl:w-[90dvw] xl:min-w-[1200px]">
              <h1 className="mb-[14px] flex flex-col leading-none mx-auto text-center">
                <span className={clsx(locale === 'zh' && 'order-2', 'self-center')}>
                  <span className={clsx(
                    archivo.className,
                    'bg-gradient-to-r from-[#3D5AEC] via-[#3D5AEC] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text text-[42px] uppercase',
                    'lg:inline-block lg:text-[78px] ',
                    'xl:text-[64px] ',
                    '2xl:text-[80px]'
                  )}>
                    {t('Core Features')}
                  </span>
                </span>
              </h1>
              <h3 className="text-[#000] mb-5 px-10 text-center text-[28px] font-[600] leading-none lg:text-[48px]">
                {t('of Cloud Office Monitoring')}
              </h3>
              <Image src={ThreeLineCircuitElement} alt="Circuit Element" className="hidden invert brightness-0 grayscale xl:block absolute right-0 top-0 opacity-100 lg:scale-150 lg:top-1/4 xl:scale-100 xl:top-8 xl:-right-20" />
              <Image src={CircuitElement} alt="Circuit Element" className="invert brightness-0 grayscale absolute right-0 top-0 opacity-100 lg:w-[30%] lg:top-5 xl:hidden" />

              <div className="embla overflow-hidden w-[105%] mt-15" ref={emblaRef}>
                <div className="embla__container flex cursor-grab">
                  {features.map((feature, idx) => (
                    <div
                      key={`feature-${idx}`}
                      className="embla__slide flex-shrink-0 w-[320px] mx-2 xl:w-[340px] xl:mx-5 2xl:w-[380px]"
                    >
                      <div className="min-h-[400px] h-full w-full relative flex flex-col rounded-[10px] text-white overflow-hidden">
                        <Image
                          src={feature.background}
                          alt=""
                          fill
                          className="object-cover opacity-70"
                        />
                        <div className="absolute inset-0 bg-gradient-to-b from-[#FFFFFF00] via-[#002C77E6] to-[#002C77E6]" />
                        <div className="relative p-6 mt-[50%]">
                          <h3 className="text-[24px] font-[600] mb-2">{feature.title}</h3>
                          <p className="text-[16px] font-[400]">{feature.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="hidden lg:flex justify-center gap-15 mt-5">
                  <button
                    onClick={scrollPrev}
                    disabled={!prevBtnEnabled}
                    className={`${!prevBtnEnabled ? 'cursor-not-allowed' : ''}`}
                  >
                    <LeftArrow className="w-full h-full" />
                  </button>

                  <button
                    onClick={scrollNext}
                    disabled={!nextBtnEnabled}
                    className={`${!nextBtnEnabled ? 'cursor-not-allowed' : ''}`}
                  >
                    <RightArrow className="w-full h-full" />
                  </button>
                </div>
              </div>

              <div className="flex justify-center mt-8 w-full gap-2 lg:hidden">
                {scrollSnaps.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => scrollTo(index)}
                    className={`w-2 h-2 rounded-full transition-colors ${index === selectedIndex ? 'bg-[#3D5AEC]' : 'bg-gray-300'}`}
                  />
                ))}
              </div>
              <Image src={CircuitElement} alt="Circuit Element" className="invert brightness-0 grayscale absolute left-0 -bottom-3 transform rotate-180 scale-110 opacity-100 xl:hidden" />
            </div>
          </div>
        </div>
      </div>
    </div >
  );
}

export default COMSection3;
