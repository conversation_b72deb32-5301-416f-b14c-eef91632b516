'use client';

import CircuitElement from '@hi7/assets/background/circuit-element.png';
import LongCircuitElement from '@hi7/assets/background/long-circuit-element.png';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import useEmblaCarousel from 'embla-carousel-react';
import { Archivo } from 'next/font/google';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

function COMSection5() {
  const t = useI18n();
  const locale = useLocale();
  // Modified Embla configuration to trim snaps and hide non-active slides
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    align: 'center',
    containScroll: 'trimSnaps', // Ensures non-active slides are not partially visible
    slidesToScroll: 1, // Scroll one slide at a time
  });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);
  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false);
  const [nextBtnEnabled, setNextBtnEnabled] = useState(true);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
    setPrevBtnEnabled(emblaApi.canScrollPrev());
    setNextBtnEnabled(emblaApi.canScrollNext());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    const originalArrayList = emblaApi.scrollSnapList();
    const filteredArrayList = Array.from(new Set(originalArrayList));
    setScrollSnaps(filteredArrayList);
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  const scrollTo = useCallback((index: number) => {
    if (!emblaApi) return;
    emblaApi.scrollTo(index);
  }, [emblaApi]);

  const features = [
    {
      title: t('Fortune 500 Case Study'),
      description:
        t('A world-renowned multinational technology company with 200+ offices worldwide faced challenges in managing cross-border teams and ensuring data security compliance. By implementing the Cloud Office Monitoring System, they established a global unified management platform, enabling multi-region collaboration and compliance supervision.'),
      insights: [
        {
          percentage: '65%',
          subtitle: t('Increase in cross-regional collaboration efficiency'),
        },
        {
          percentage: '99%',
          subtitle: t('Compliance rate for data security regulations'),
        },
        {
          percentage: '45%',
          subtitle: t('Reduction in management costs'),
        },
      ],
    },
    {
      title: t('Internet Industry Case Study'),
      description:
        t('A leading internet company, experiencing rapid growth, struggled with managing remote teams across different regions and quantifying employee productivity. By deploying Cloud Office Monitoring, they built a unified remote collaboration management platform, enabling work performance assessments and team efficiency analysis.'),
      insights: [
        {
          percentage: '45%',
          subtitle: t('Improvement in team collaboration efficiency'),
        },
        {
          percentage: '30%',
          subtitle: t('Reduction in project delivery cycles'),
        },
        {
          percentage: '40%',
          subtitle: t('Lower costs, support agile development & innovation'),
        },
      ],
    },
    {
      title: t('Global Consumer Goods Group'),
      description:
        t('This $50 billion revenue consumer goods giant operates in 100+ countries. Faced with inefficient cross-time-zone collaboration, supply chain data risks, and compliance challenges across multiple countries, we designed a custom global office monitoring solution with a 24/7 operational management platform.'),
      insights: [
        {
          percentage: '65%',
          subtitle: t('Faster global response with multilingual support & data protection'),
        },
        {
          percentage: '99%',
          subtitle: t('Reduction in security incidents'),
        },
        {
          percentage: '40%',
          subtitle: t('Lower costs with secure, efficient global office management'),
        },
      ],
    },
  ];

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-[--min-sm-h-hvh] overflow-hidden bg-[linear-gradient(180deg,_#FFFFFF,_#B5E1FF_50%,_#FFFFFF)] md:min-h-[60vh] lg:pt-0 lg:pb-0 xl:min-h-[100vh] xl:pb-[50px]">
          <div className="relative flex flex-col items-center justify-center py-11 md:py-0 lg:pt-[70px]">
            <div className="w-[100%] lg:w-[100dvw] lg:min-w-[855px] xl:w-[100dvw] xl:min-w-[900px]">
              <h1 className="mb-[14px] flex flex-col leading-none mx-auto text-center">
                <span
                  className={clsx(
                    archivo.className,
                    'bg-gradient-to-r from-[#3D5AEC] via-[#3D5AEC] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text text-[42px] uppercase',
                    'lg:inline-block lg:text-[78px]',
                    'xl:text-[64px]',
                    '2xl:text-[80px]',
                  )}
                >
                  {t('Case Studies')}
                </span>
              </h1>
              <h3 className="text-[#000] mb-5 px-0 text-center text-[28px] font-[600] leading-none lg:text-[48px]">
                {t('from Different Industries')}
              </h3>
              <Image
                src={CircuitElement}
                alt="Circuit Element"
                className="invert brightness-0 grayscale absolute right-0 top-0 opacity-100 lg:w-[25%] xl:w-[25%] lg:top-40 2xl:w-[25%] 2xl:top-30"
              />

              <div
                className="embla overflow-hidden w-full mx-auto py-5 px-5 mt-10 lg:px-[100px] xl:px-[150px] xl:mt-0 2xl:px-[300px]"
                ref={emblaRef}
                style={{ overflow: 'hidden' }}
              >
                <div className="embla__container flex cursor-grab">
                  {features.map((feature, idx) => (
                    <div
                      key={`feature-${idx}`}
                      className={`${selectedIndex === idx ? 'opacity-100 lg:opacity-100' : 'opacity-0 lg:opacity-100'} w-full embla__slide my-0 mx-auto flex-shrink-0 w-full bg-white rounded-[16px] bg-[linear-gradient(180deg,_#FFFFFF,_#B5E1FF66_70%,_#FFFFFF)] shadow-[0px_4px_24px_0px_rgba(0,0,0,0.2)] lg:w-[90%] lg:mx-[40px] xl:py-5 xl:px-7 2xl:py-10`}
                      style={{
                        transition: 'opacity 0.3s ease-in-out',
                      }}
                    >
                      <div className="min-h-[400px] h-full w-full relative flex flex-col rounded-[10px] overflow-hidden py-10 px-6 md:px-10 lg:px-18 xl:py-0 justify-around">

                        {/****** Case Study Content ******/}
                        <div className="mb-8">
                          <h3 className="text-[24px] font-[600] mb-4 text-[#3D5AEC]">{feature.title}</h3>
                          <p className="text-[16px] font-[400] text-gray-700">{feature.description}</p>
                        </div>

                        {/****** Insights Section ******/}
                        <div className="grid grid-cols-1 gap-6 mt-auto lg:grid-cols-3 lg:gap-0 xl:-mt-1">
                          {feature.insights.map((insight, i) => (
                            <>
                              <div
                                key={`insight-${i}`}
                                className={clsx(
                                  'w-full py-6 lg:py-0',
                                  i !== feature.insights.length - 1 && 'lg:border-r lg:border-[#33B2ED] lg:pr-5',
                                  i !== 0 && 'lg:pl-6',
                                )}
                              >
                                <h1 className="mb-[14px] flex flex-col leading-none mx-auto">
                                  <span
                                    className={clsx(
                                      'bg-gradient-to-r from-[#3D5AEC] via-[#33B2ED] to-[#33B2ED] font-[600] inline-block text-transparent bg-clip-text text-[42px]',
                                      'lg:text-[64px]',
                                      '2xl:text-[80px]',
                                    )}
                                  >
                                    {insight.percentage}
                                  </span>
                                </h1>
                                <div className="text-[16px] font-[400] text-[#3D5AEC] -mt-2 lg:-mt-3">
                                  {insight.subtitle}
                                </div>
                              </div>
                              {i !== feature.insights.length - 1 && (
                                <hr className="lg:hidden border-[#33B2ED]" />
                              )}
                            </>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-center mt-8 w-full gap-2">
                {scrollSnaps.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => scrollTo(index)}
                    className={`w-2 h-2 rounded-full transition-colors ${index === selectedIndex ? 'bg-[#3D5AEC]' : 'bg-gray-300'
                      }`}
                  />
                ))}
              </div>
              <Image
                src={LongCircuitElement}
                alt="Circuit Element"
                className="hidden xl:block absolute left-0 -bottom-10 transform xl:-left-20 xl:scale-80 2xl:scale-115 opacity-100"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default COMSection5;