'use client';

import { useI18n } from '@hi7/lib/i18n';

import CloudOfficeMonitoringHeroBg from '@hi7/assets/background/cloud-office-monitoring-hero-bg.png';
import HeroImg from '@hi7/assets/background/cloud-office-monitoring-hero-img.png';
import useScreenSize from '@hi7/helpers/useScreenSize';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import { Archivo } from 'next/font/google';
import Image from 'next/image';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

// import LinkButton from '../Form/LinkButton';

function Hero() {
  const locale = useLocale();
  const t = useI18n();
  const { isMobile } = useScreenSize();

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) bg-white overflow-hidden bg-[#FFFFFF] md:h-screen lg:min-h-(--min-sm-h-hvh) lg:pt-[50px] xl:min-h-(--min-sm-h-hvh) xl:h-screen xl:pt-[0px] 2xl:h-screen flex items-center">
          <Image src={CloudOfficeMonitoringHeroBg} alt="Hero Background" className="absolute object-cover bg-center inset-0 bg-bottom-right w-full h-full opacity-70" />
          <div className="container mx-auto px-4 flex flex-col xl:flex-row items-center justify-center relative z-10 py-16 lg:py-24">
            <div className="w-full xl:w-3/5 px-0 mt-10 text-center mb-8 lg:pl-10 lg:text-left xl:w-screen xl:-mt-5 xl:ml-8 2xl:w-full 2xl:pl-0">
              <h1 className="mb-[14px] flex flex-col leading-none mx-auto">
                <span className={clsx(
                  archivo.className,
                  'bg-gradient-to-r from-[#2243EA] via-[#2243EA] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text text-[42px] uppercase',
                  'lg:inline-block lg:text-[78px]',
                  'xl:text-[86px]',
                  '2xl:text-[120px]'
                )}>
                  {t('Cloud Office Monitoring')}
                </span>
              </h1>
              <p className="text-[16px] font-[400] lg:text-xl mb-8 text-black xl:w-3/5">
                {t('Providing a comprehensive enterprise computer monitoring solution for efficient business management.')}
              </p>
              <button className="mb-5 bg-linear-[135deg,#2243EA_0%,#33B2ED] font-[500] text-[14px] text-white mx-auto items-center justify-center px-6 py-2 rounded-md font-medium hover:bg-opacity-90 transition">
                {t('Try For Free')}
              </button>
            </div>

            <div className="block w-1/2 mx-auto w-full xl:w-2/5 xl:absolute xl:translate-x-[40%]">
              <Image src={HeroImg} alt="Hero Image" className="mx-auto scale-110 -translate-y-[25%] translate-x-7 md:-translate-y-[10%] md:translate-x-8 lg:translate-x-10 lg:scale-130 xl:scale-140 xl:translate-x-10 xl:translate-y-10 2xl:mt-15 2xl:-mr-15 2xl:scale-150 2xl:translate-x-17" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Hero;
