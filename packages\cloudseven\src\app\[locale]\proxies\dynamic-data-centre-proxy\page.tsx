import ProxyTemplate from '@hi7/components/ProxyTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Dynamic Data Center IP - Dynamic Data Center IP as low as $0.6/GB - ElfProxy Proxy IP Pool',
    ),
    description: t(params.locale)(
      'Free trial of global dynamic data center IPs (data center proxy IPs). Automatically rotating IP addresses, lowest cost to obtain high anonymity Socks5 proxy IPs and HTTP(s) proxy IPs, carefully selected high-quality data center IPs with global relay acceleration, ensuring 99.9% business uptime.',
    ),
    keywords: t(params.locale)(
      'Dynamic IP, Dynamic Data Center IP, Data Center Proxy, Data Center IP, Buy Data Center IP, Try Data Center IP',
    ),
  };
};

function Proxy() {
  return <ProxyTemplate id="proxy-2" />;
}

export default Proxy;
