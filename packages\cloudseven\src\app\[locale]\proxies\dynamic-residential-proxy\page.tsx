import ProxyTemplate from '@hi7/components/ProxyTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Dynamic Residential Proxy - Dynamic IP as low as $2.4/GB - ElfProxy Pure IP Proxy Pool',
    ),
    description: t(params.locale)(
      'Global pool of over 100 million pure residential IPs, free trial of dynamic residential IP. Each IP is sourced from residential IP addresses of different devices worldwide, ensuring high IP purity, anonymous access, and no IP blocking. We also provide HTTP proxies, Socks5 proxies, and private residential IPs.',
    ),
    keywords: t(params.locale)(
      'Dynamic IP, Dynamic Proxy, Dynamic Residential IP, Dynamic Residential Proxy, Buy Dynamic IP, Try Dynamic IP, IP Proxy Pool',
    ),
  };
};

function Proxy() {
  return <ProxyTemplate id="proxy-1" />;
}

export default Proxy;
