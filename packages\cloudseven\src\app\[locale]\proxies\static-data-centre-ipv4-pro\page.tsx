import ProxyTemplate from '@hi7/components/ProxyTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Static Data Center IP Proxy - Relay IP/Accelerated IP - Global Network Latency 20-80ms',
    ),
    description: t(params.locale)(
      'Global high-stability data center IP free trial, with relay acceleration achieving 20-80ms latency, just like using an international dedicated line, each IP is dedicated for exclusive use. Suitable for high-stability, low-latency businesses: enterprise operations, international data transmission, and more. 99.9% online stability, fixed IP and long-term IP for long-term use.',
    ),
    keywords: t(params.locale)(
      'Dedicated Line Relay, Node Relay, Dedicated Line Acceleration, Relay IP, Accelerated IP, Data Center IP Relay, IP Latency',
    ),
  };
};

function Proxy() {
  return <ProxyTemplate id="proxy-6" />;
}

export default Proxy;
