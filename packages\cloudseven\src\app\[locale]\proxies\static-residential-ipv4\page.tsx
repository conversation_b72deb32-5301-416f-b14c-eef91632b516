import ProxyTemplate from '@hi7/components/ProxyTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Static Residential IP Proxy - ISP Proxy/Broadband IP - ElfProxy Pure Residential IP',
    ),
    description: t(params.locale)(
      'Global pure residential proxy IPs with free trial, just like using a local real home network, high anonymity dedicated IP, ensuring your business/account is more stable and secure. Provides static residential IP, static ISP proxy, dedicated IP, fixed IP, supports HTTP(s) proxy and Socks5 proxy protocols.',
    ),
    keywords: t(params.locale)(
      'Static IP, Residential IP, Static Residential, Static Residential Proxy, Static Residential IP, Static Residential IP Trial, Static IP Purchase',
    ),
  };
};

function Proxy() {
  return <ProxyTemplate id="proxy-4" />;
}

export default Proxy;
