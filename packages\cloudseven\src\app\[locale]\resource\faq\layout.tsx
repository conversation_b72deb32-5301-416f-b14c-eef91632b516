import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      "Proxy IP FAQs - Beginner's Guide, Protocol Support, and Free Trial Instructions",
    ),
    description: t(params.locale)(
      'ElfProxy provides solutions for frequently asked questions about IP selection, renewal process, number of devices connected, connection failures, supported protocols (SOCKS5/HTTP/HTTPS), fingerprint browser compatibility, and free trials. Get solutions immediately!',
    ),
    keywords: t(params.locale)(
      'Proxy IP, Proxy IP FAQs, Proxy IP Common Questions, How to Choose Proxy IP, Proxy IP Device Connections, Proxy IP Troubleshooting, SOCKS5 Proxy, HTTP Proxy, HTTPS Proxy, Proxy Protocols, Fingerprint Browser Proxy, Proxy IP Free Trial',
    ),
  };
};

export default function FAQLayout({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}
