import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Best Ad Verification Proxy - 99.99% Fraud Detection Rate - Global Localized Ad Verification',
    ),
    description: t(params.locale)(
      'ElfProxy provides real residential IP proxy services with a global network covering over 220 countries and cities of real residential IP resources. Our solution supports localized ad display effect verification, competitor ad intelligence gathering, and brand safety protection. With a 99.99% invalid traffic detection rate, we ensure zero waste of your ad budget. Start your free trial now!',
    ),
    keywords: t(params.locale)(
      'Ad Verification Proxy IP, Localized Ad Detection, Ad Fraud Detection, Competitor Ad Detection, Global IP Pool, Dynamic Residential IP, Residential Proxy IP',
    ),
  };
};

function Solution() {
  return <SolutionTemplate id="solution-4" />;
}

export default Solution;
