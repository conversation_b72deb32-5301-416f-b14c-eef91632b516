import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Best Brand Protection Proxy - Global Residential IP Monitoring - Precise Infringement Evidence Tracking.',
    ),
    description: t(params.locale)(
      "ElfProxy provides over 220 country-city level dynamic and static residential IPs. Our real residential network helps you accurately monitor brand infringement activities. Through our dynamic IPs, you can perform massive accesses that are indistinguishable from real users, breaking through regional restrictions to comprehensively protect your brand's reputation and collect essential evidence. Start your free trial now!",
    ),
    keywords: t(params.locale)(
      'brand protection, brand infringement monitoring, intellectual property protection, brand protection proxy, dynamic IP, global residential IP, dynamic residential IP',
    ),
  };
};

function Solution() {
  return <SolutionTemplate id="solution-7" />;
}

export default Solution;
