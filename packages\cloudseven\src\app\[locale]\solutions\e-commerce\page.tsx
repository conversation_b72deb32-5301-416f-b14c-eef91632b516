import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Best E-commerce Data Collection Proxy - 99.5% Scraping Success Rate - Proxy IP Anti-Ban.',
    ),
    description: t(params.locale)(
      'ElfProxy provides residential IP proxy services for cross-border e-commerce enterprises, with a global network covering over 220 countries and cities, allowing precise targeting of markets such as New York, USA. It enables real-time collection of product prices, reviews, and inventory data from platforms like Amazon and eBay. It solves problems like frequent CAPTCHAs and IP blocking. Start your free trial now!',
    ),
    keywords: t(params.locale)(
      'Cross-border e-commerce data collection, Amazon price monitoring, Amazon data collection, eBay price monitoring, eBay data collection, crawler IP, crawler proxy, e-commerce crawler IP.',
    ),
  };
};

function Solution() {
  return <SolutionTemplate id="solution-6" />;
}

export default Solution;
