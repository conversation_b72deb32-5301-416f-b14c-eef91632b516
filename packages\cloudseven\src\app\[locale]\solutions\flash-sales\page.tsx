import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Best Sneaker Copping Proxy - Instant Nike/Yeezy Purchases - Prevent Bans with Geo-Targeted Residential IPs',
    ),
    description: t(params.locale)(
      'ElfProxy provides top-tier dynamic residential proxies specifically for copping limited edition sneakers. With precise geo-targeting of popular countries and cities like the US, Japan, and the UK, our service achieves a 99.9% success rate in bypassing IP detection on Nike and other platforms. It supports running multiple accounts simultaneously without association risks, and you can try it for free to experience ultra-fast sneaker purchases!',
    ),
    keywords: t(params.locale)(
      'sneaker copping proxy, sneaker proxy IP, residential IP for sneakers, Nike anti-ban proxy, Yeezy anti-ban proxy, Adidas sneaker proxy, dynamic IP proxy, geo-targeted sneaker proxy',
    ),
  };
};

function Solution() {
  return <SolutionTemplate id="solution-9" />;
}

export default Solution;
