import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Best Market Research Proxy - 99.9% Data Accuracy - Global 100 Million Residential IPs Anti-Ban',
    ),
    description: t(params.locale)(
      'ElfProxy offers high-anonymity residential proxy IP services with global residential IP resources from over 220 countries and cities. It supports competitor monitoring, consumer behavior analysis, and public opinion tracking. By overcoming anti-crawling mechanisms, it achieves a data collection accuracy rate of up to 99.9%, providing a scientific basis for market prediction.',
    ),
    keywords: t(params.locale)(
      'Market Research Proxy IP, Global Market Survey, Data Collection IP, Competitor Data Monitoring, Market Research, Dynamic Residential IP, Dynamic IP, Global Residential IP',
    ),
  };
};

function Solution() {
  return <SolutionTemplate id="solution-2" />;
}

export default Solution;
