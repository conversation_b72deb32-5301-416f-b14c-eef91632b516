import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Best SEO Monitoring Proxy - SERP Data Monitoring - Avoid IP Blocking Risks.',
    ),
    description: t(params.locale)(
      'ElfProxy offers SEO monitoring proxy IP services, leveraging a global network of dynamic residential IPs to real-time track keyword rankings, search trends, and competitor data, providing precise insights into industry trends and competitor activities. Enjoy unrestricted access to Google and other search engines while avoiding the risk of IP blocking. Start your free trial now!',
    ),
    keywords: t(params.locale)(
      'SEO Monitoring Optimization, Search Engine Data Monitoring, Search Engine Monitoring, Website Ranking Optimization, SEO Keyword Optimization, SEO Analysis, Dynamic IP',
    ),
  };
};

function Solution() {
  return <SolutionTemplate id="solution-10" />;
}

export default Solution;
