import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Best Social Media Proxies - Real Residential IPs for Safe Multi-Account Marketing',
    ),
    description: t(params.locale)(
      'ElfProxy provides high-quality residential proxy services with genuine IPs from real home broadband networks across 220+ countries. Our proxies effectively hide your real IP and encrypt data transmission, ensuring your privacy and security. Overcome geo-restrictions to access social media platforms like TikTok, Facebook, and more. Manage multiple accounts with independent IPs, preventing association and enhancing safety. Solve common issues such as TikTok registration failures, zero video views, and Facebook mass bans. Our local native IPs ensure more accurate and increased local traffic delivery. Start your free trial now!',
    ),
    keywords: t(params.locale)(
      'TikTok Registration, TikTok Registration Issues, TikTok Proxy IP, Facebook Proxy IP, Instagram Proxy IP, Twitter Proxy IP, LinkedIn Proxy IP, YouTube Proxy IP',
    ),
  };
};

function Solution() {
  return <SolutionTemplate id="solution-1" />;
}

export default Solution;
