import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Best Residential Proxies - Hide Your IP, Access Globally, Manage Multiple Accounts Securely',
    ),
    description: t(params.locale)(
      'ElfProxy provides high-anonymity residential proxy services with genuine residential IPs from over 220 countries. Effectively hide your real IP address and encrypt data transmission. Bypass geo-restrictions to access platforms like Netflix, Google, and more. Support independent IP isolation for managing multiple web accounts, enhancing security. Start your free trial now!',
    ),
    keywords: t(params.locale)(
      'Web browsing proxy IP, high-anonymity IP, global network acceleration, multi-account anti-association, web account anti-association',
    ),
  };
};

function Solution() {
  return <SolutionTemplate id="solution-3" />;
}

export default Solution;
