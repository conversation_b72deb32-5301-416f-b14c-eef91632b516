import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Best Web Scraping Proxy - 99.7% Scraping Success Rate - Global 100 Million Residential IP Pool.',
    ),
    description: t(params.locale)(
      'ElfProxy provides high-anonymity residential proxy services, featuring a vast network across more than 220 countries and cities with both dynamic and static IP options. Our solution is designed for large-scale concurrent data collection, effectively bypassing anti-scraping measures on major platforms like Amazon and TikTok. It resolves issues such as IP blocking and CAPTCHA verification, achieving an impressive 99.7% success rate in data collection. Start your free trial today!',
    ),
    keywords: t(params.locale)(
      'web data scraping, web data collection, crawler proxy IP, data collection IP, high-anonymity proxy IP, dynamic IP, global IP pool, competitor data monitoring',
    ),
  };
};

function Solution() {
  return <SolutionTemplate id="solution-5" />;
}

export default Solution;
