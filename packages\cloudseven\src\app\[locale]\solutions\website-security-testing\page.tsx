import SolutionTemplate from '@hi7/components/SolutionTemplate';
import { type Locale } from '@hi7/lib/i18n';
import { t } from '@hi7/lib/i18n/ssr';

export const generateMetadata = async ({
  params,
}: Readonly<{
  params: { locale: Locale };
}>) => {
  return {
    title: t(params.locale)(
      'Best Website Testing Proxy - Global Residential Proxy IP Testing - Website Speed Optimization',
    ),
    description: t(params.locale)(
      'ElfProxy offers global residential and data center IPs with extensive IPv4 and IPv6 resources. Accurately simulate user access from over 220 regions to support website security testing, speed monitoring, vulnerability scanning, firewall penetration testing, and identity verification drills. Identify data breach risks and ensure stable multi-region access. Start your free trial now!',
    ),
    keywords: t(params.locale)(
      'website security testing, website speed testing, proxy IP testing, global proxy IP testing, multi-region access testing, dynamic residential IP, global residential IP, authentic residential IP',
    ),
  };
};

function Solution() {
  return <SolutionTemplate id="solution-8" />;
}

export default Solution;
