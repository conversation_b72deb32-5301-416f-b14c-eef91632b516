'use client';

import Minus from '@hi7/assets/icon/minus.svg';
import Plus from '@hi7/assets/icon/plus.svg';
import type { AccordionProps } from '@hi7/interface/accordion';
import { useState } from 'react';

const Accordion = ({ items, multiple = false }: AccordionProps) => {
  const [open, setOpen] = useState<number[]>([0]);

  const toggleItem = (index: number) => {
    setOpen((prev) => {
      if (multiple) {
        return prev.includes(index)
          ? prev.filter((i) => i !== index)
          : [...prev, index];
      } else {
        return prev[0] === index ? [] : [index];
      }
    });
  };

  return (
    <div className="flex w-full cursor-pointer flex-col gap-5">
      {items.map((item, index) => (
        <div
          key={`acc-${index}`}
          onClick={() => toggleItem(index)}
          className="shadow-hi7-shadow w-full min-w-80 gap-10 rounded-2xl px-5 py-4"
        >
          <div className="grid grid-cols-[auto_1fr_auto] items-center gap-4">
            <span className="col-start-1">{item.icon}</span>
            <h4 className="text-hi7-dark-blue text-[18px] font-bold lg:text-[22px]">
              {item.title}
            </h4>

            <span className="col-start-3 justify-self-end">
              {open.includes(index) ? <Minus /> : <Plus />}
            </span>
          </div>

          {open.includes(index) && (
            <p className="text-hi7-deep-dark pe-7 lg:ml-18">{item.content}</p>
          )}
        </div>
      ))}
    </div>
  );
};

export default Accordion;
