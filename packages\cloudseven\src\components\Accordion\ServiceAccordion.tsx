'use client';

import Minus from '@hi7/assets/icon/minus.svg';
import Plus from '@hi7/assets/icon/plus.svg';
import type { AccordionProps } from '@hi7/interface/accordion';
import type { ServiceSectionId } from '@hi7/interface/info-card';
import clsx from 'clsx';
import { useEffect, useState } from 'react';

const ServiceAccordion = ({
  items,
  multiple = false,
  activeId,
}: AccordionProps) => {
  const [openIds, setOpenIds] = useState<ServiceSectionId[]>(['campaign']);

  useEffect(() => {
    if (activeId) {
      setOpenIds([activeId]);
    }
  }, [activeId]);

  const toggleItem = (id: ServiceSectionId) => {
    setOpenIds((prevIds) => {
      if (multiple) {
        return prevIds.includes(id)
          ? prevIds.filter((itemId) => itemId !== id)
          : [...prevIds, id];
      } else {
        return prevIds[0] === id ? [] : [id];
      }
    });
  };

  return (
    <div className="flex w-full cursor-pointer flex-col">
      {items.map((item) => (
        <div
          id={item.id}
          key={item.id}
          onClick={() => toggleItem(item.id as ServiceSectionId)}
          className={clsx(
            'border-t-hi7-border w-full min-w-80 scroll-mt-40 gap-10 border border-s-0 border-e-0 py-7 lg:px-5',
            item.id === items[items.length - 1].id
              ? 'border-b-hi7-border'
              : 'border-b-0',
          )}
        >
          <div className="grid grid-cols-[auto_1fr_auto] items-center justify-start">
            <span className="col-start-1 scale-75 lg:scale-100 lg:pe-32">
              {item.icon}
            </span>
            <h4
              className={clsx(
                'col-start-2 text-[18px] leading-tight font-bold lg:text-2xl',
                {
                  'text-hi7-primary': openIds.includes(
                    item.id as ServiceSectionId,
                  ),
                },
              )}
            >
              {item.title}
            </h4>

            <span className="col-start-3 justify-self-end">
              {openIds.includes(item.id as ServiceSectionId) ? (
                <Minus />
              ) : (
                <Plus />
              )}
            </span>

            <div
              className={clsx(
                'col-span-3 flex flex-col gap-5 ps-3 pe-4 text-[14px] lg:col-span-2 lg:col-start-2 lg:ps-0 lg:text-[16px]',
              )}
            >
              {openIds.includes(item.id as ServiceSectionId) && (
                <>
                  <p className="text-hi7-deep-dark mt-3 pe-7">{item.content}</p>

                  <div className="grid ps-6 lg:grid-cols-2">
                    {item.keypoints?.map((keypoint, index) => (
                      <ul key={index} className="list-disc">
                        <li>{keypoint}</li>
                      </ul>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ServiceAccordion;
