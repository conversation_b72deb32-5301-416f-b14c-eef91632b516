'use client';

import clsx from 'clsx';
import { Archivo } from 'next/font/google';
import { ReactNode } from 'react';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

interface BannerProps {
  title: string;
  subtitle?: string;
  highlightText?: string;
  ctaText?: string;
  backgroundImage?: string;
  children?: ReactNode;
}

export default function banner({
  title,
  subtitle = '',
  highlightText = '',
  ctaText = 'Try For Free',
  backgroundImage = '/videos/header-bg.mp4',
  children,
}: BannerProps) {
  return (
    <div className="relative w-full min-h-[60vh] h-screen flex items-center justify-center">
      {/* Background Image */}
      <div className="absolute inset-0">
        <video
          autoPlay
          muted
          loop
          playsInline
          className="object-cover w-full h-full z-[-1] opacity-100"
          src={backgroundImage}
        />
        <div className="absolute inset-0 bg-black/30" /> {/* Overlay for better text contrast */}
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 relative z-10 text-center">
        {subtitle && (
          <h2 className="text-[24px] text-black font-medium mb-2 xl:text-[48px] xl:font-[600]">
            {subtitle}
          </h2>
        )}

        <h1 className="mb-[14px] flex flex-col leading-none mx-auto">
          <span
            className={clsx(
              archivo.className,
              'bg-gradient-to-r from-[#3D5AEC] via-[#3D5AEC] to-[#33B2ED] font-[900] text-[40px] inline-block text-transparent bg-clip-text',
              'lg:text-[78px]',
              'xl:text-[96px]',
            )}
          >
            {title}
          </span>
        </h1>

        {highlightText && (
          <p className="font-[400] text-[16px] xl:font-[500] mb-6 font-medium md:text-xl xl:text-[20px]">
            {highlightText}
          </p>
        )}

        {children || (

          <button className="mb-5 bg-linear-[135deg,#2243EA_0%,#2243EA_40%,#33B2ED] font-[500] text-[14px] text-white mx-auto items-center justify-center px-6 py-2 rounded-md font-medium hover:bg-opacity-90 transition">
            {ctaText}
          </button>
        )}
      </div>
    </div>
  );
}