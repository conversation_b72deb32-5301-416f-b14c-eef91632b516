'use client';

import clsx from 'clsx';
import { Archivo } from 'next/font/google';
import { ReactNode } from 'react';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

interface BannerProps {
  title: string;
  subtitle?: string;
  highlightText?: string;
  ctaText?: string;
  backgroundImage?: string;
  children?: ReactNode;
}

export default function banner({
  title,
  subtitle = '',
  highlightText = '',
  ctaText = 'Try For Free',
  backgroundImage = '/videos/header-bg.mp4',
  children,
}: BannerProps) {
  return (
    <div
      className="relative flex h-screen min-h-[60vh] w-full items-center justify-center"
      data-scroll-section
    >
      {/* Background Image */}
      <div className="absolute inset-0">
        <video
          autoPlay
          muted
          loop
          playsInline
          className="z-[-1] h-full w-full object-cover opacity-100"
          src={backgroundImage}
        />
        <div className="absolute inset-0 bg-black/30" />{' '}
        {/* Overlay for better text contrast */}
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 text-center">
        {subtitle && (
          <h2 className="mb-2 text-[24px] font-medium text-black xl:text-[48px] xl:font-[600]">
            {subtitle}
          </h2>
        )}

        <h1 className="mx-auto mb-[14px] flex flex-col leading-none">
          <span
            className={clsx(
              archivo.className,
              'inline-block bg-gradient-to-r from-[#3D5AEC] via-[#3D5AEC] to-[#33B2ED] bg-clip-text text-[40px] font-[900] text-transparent',
              'lg:text-[78px]',
              'xl:text-[96px]',
            )}
          >
            {title}
          </span>
        </h1>

        {highlightText && (
          <p className="mb-6 text-[16px] font-[400] font-medium md:text-xl xl:text-[20px] xl:font-[500]">
            {highlightText}
          </p>
        )}

        {children || (
          <button className="hover:bg-opacity-90 mx-auto mb-5 items-center justify-center rounded-md bg-linear-[135deg,#2243EA_0%,#2243EA_40%,#33B2ED] px-6 py-2 text-[14px] font-[500] font-medium text-white transition">
            {ctaText}
          </button>
        )}
      </div>
    </div>
  );
}
