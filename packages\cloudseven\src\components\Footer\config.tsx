import type { MenuLinkProps } from '@hi7/interface/link';
import { ROUTES } from '../Header/config';

export const QUICK_ACCESS: MenuLinkProps[] = [
  { url: '/product-introduction/cloud-office-monitoring', children: 'Cloud Office Monitoring' },
  { url: '/product-introduction/cloud-desktop', children: 'Cloud Desktop' },
  { url: '/pricing', children: 'Pricing' },
  { url: 'https://blog.elfproxy.com', children: 'Industry News' },
  { url: '/download', children: 'Download' },
];

export const PROXIES: MenuLinkProps[] = (
  ROUTES.find((s) => s.url === '/proxies')?.items || []
).map((item) => ({
  url: item.url,
  children: item.text,
  order: item.order
    ?.split(' ')
    .filter((s) => !s.startsWith('lg:'))
    .join(' '),
}));

export const SOLUTIONS: MenuLinkProps[] = (
  ROUTES.find((s) => s.url === '/solutions')?.items || []
).map((item) => ({
  url: item.url,
  children: item.text,
  order: item.order
    ?.split(' ')
    .filter((s) => !s.startsWith('lg:'))
    .join(' '),
}));
