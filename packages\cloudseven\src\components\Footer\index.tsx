'use client';

import Logo from '@hi7/assets/logo/logo.svg';
import { useI18n } from '@hi7/lib/i18n';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import Link from '../Link';
import { QUICK_ACCESS } from './config';

const Footer = () => {
  const t = useI18n();
  const pathname = usePathname();

  return (
    <footer
      className={clsx(
        'relative z-0 flex transform flex-col gap-[30px] px-4 py-15 text-white transition-all duration-200',
        'bg-linear-[90deg,#2243EA_0.54%,#33B2ED]',
        'md:px-10',
        'lg:px-10 lg:py-20',
        'xl:px-20',
        '2xl:',
      )}
      data-scroll-section
    >
      <div className="flex w-full flex-col gap-[40px] lg:flex-row lg:gap-[60px]">
        <div
          className={clsx(
            'flex flex-2 flex-col items-start justify-start gap-[20px]',
            'lg:flex-10 lg:gap-5',
            'xl:flex-12',
            '2xl:flex-4',
          )}
        >
          <Link url={''}>
            <Logo width="100%" height={38} />
          </Link>
          <p className="text-[14px] font-[400]">
            {t(
              'CloudSeven – Creating a secure and private remote working environment for businesses. Our intelligent monitoring system provides real-time employee activity tracking, preventing data breaches and unauthorised transactions, ensuring comprehensive data security. A one-stop solution for enterprise management challenges.',
            )}
          </p>
        </div>

        <div className="hidden flex-[1.5] lg:block xl:flex-[1.8]"></div>

        <div className="flex flex-[0.8] flex-col gap-2.5 whitespace-nowrap xl:flex-[1.8]">
          <p className="text-[16px] font-[700] text-white">
            {t('Products & Services')}
          </p>

          {QUICK_ACCESS.map((item) => (
            <Link
              key={`qs-${item.url}`}
              url={item.url}
              className="text-[16px] font-[400]"
            >
              {t(item.children as string)}
            </Link>
          ))}
        </div>

        <div className="flex flex-col gap-2.5 xl:flex-[2]">
          <p className="text-[14px] font-[600] text-white">{t('Support')}</p>
          <a
            href="https://007tg.com/ccs/elfproxy"
            target="_blank"
            rel="noopener noreferrer"
            className="flex gap-2 text-[16px] font-[400]"
          >
            <div className="">{t('Telegram')}:</div>
            <a className="leading-[1.4] text-white underline hover:text-[#214A]">
              {t('Chat with us')}
            </a>
          </a>
          <a
            href="https://007tg.com/ccs/elfproxy_wa"
            target="_blank"
            rel="noopener noreferrer text-[16px] font-[400]"
            className="flex gap-2 text-[16px] font-[400]"
          >
            <div className="">{t('WhatsApp')}:</div>
            <p className="leading-[1.4] text-white underline hover:text-[#214A]">
              {t('Chat with us')}
            </p>
          </a>
          <a
            href="https://t.me/hgc007"
            target="_blank"
            rel="noopener noreferrer"
            className="flex gap-2 text-[16px] font-[400]"
          >
            <div className="">{t('Feedback')}:</div>
            <a
              href="https://t.me/hgc007"
              className="leading-[1.4] text-white underline hover:text-[#214A]"
            >
              https://t.me/hgc007
            </a>
          </a>
          <a
            href="mailto:<EMAIL>"
            target="_blank"
            rel="noopener noreferrer"
            className="flex gap-2 text-[16px] font-[400]"
          >
            <div className="">{t('Email')}:</div>
            <a className="leading-[1.4] text-white underline hover:text-[#214A]">
              <EMAIL>
            </a>
          </a>
        </div>
      </div>

      <div className="flex flex-col gap-[24px] text-[14px] leading-[1.4] lg:flex-row lg:gap-0">
        <p className="flex-1">
          Copyright © {new Date().getFullYear()} CloudSeven.{' '}
          {t('All Rights Reserved')}
        </p>

        <div className="flex gap-[24px] text-[14px] font-[400]">
          <a href="">{t('Privacy')}</a>
          <a href="">{t('Terms of use')}</a>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
