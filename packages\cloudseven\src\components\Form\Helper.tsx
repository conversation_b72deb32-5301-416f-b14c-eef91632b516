import type React from 'react';
import { type ReactNode } from 'react';
import { useController, useFormContext } from 'react-hook-form';

type HelperProps = {
  children?: string | ReactNode;
  name: string;
  classes?: string;
};

const Helper: React.FC<HelperProps> = ({ children, name, classes = '' }) => {
  const { control } = useFormContext();
  const { fieldState } = useController({ name, control });

  const errorMessage = fieldState?.error?.message;

  return (
    <p
      className={`text-sm text-${errorMessage ? 'hi7-error' : 'hi7-placeholder'} ${classes}`}
    >
      {errorMessage || children}
    </p>
  );
};

export default Helper;
