import type React from 'react';
import { type ReactNode } from 'react';

import clsx from 'clsx';
import Link from '../Link';

type HelperProps = {
  children: string | ReactNode;
  disabled?: boolean;
  type: 'main' | 'secondary' | 'text';
  size: 'L' | 'M' | 'S';
  url: string;
  target?: string;
  className?: string;
  onClick?: (e: any) => void;
};

const LinkButton: React.FC<HelperProps> = ({
  children,
  type,
  size,
  url,
  target,
  disabled,
  className,
  onClick,
}) => {
  return (
    <Link
      url={url}
      target={target}
      className={clsx(
        type !== 'text' && size === 'L' && 'px-6 py-3',
        type !== 'text' && size === 'M' && 'px-5 py-2',
        type !== 'text' && size === 'S' && 'px-3 py-1',
        size === 'L' && 'text-[16px]',
        size === 'M' && 'text-[14px]',
        size === 'S' && 'text-[12px]',
        'group flex cursor-pointer gap-2 rounded-md font-semibold whitespace-nowrap transition-all',
        type === 'main' &&
        'bg-[#FFFFFF] text-[#3D5AEC] hover:bg-[#31a0d3] hover:text-[#1E1E1E] focus:bg-[#31a0d3]',
        type === 'secondary' &&
        'bg-[#FFFFFF] text-[#3D5AEC] hover:bg-[#31a0d3] hover:text-[#1E1E1E] focus:text-[#31a0d3]',
        type === 'text' && 'bg-transparent text-[#1E1E1E] focus:text-[#31a0d3]',
        className,
      )}
      onClick={onClick}
    >
      {children}
    </Link>
  );
};

export default LinkButton;
