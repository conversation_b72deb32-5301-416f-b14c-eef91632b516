'use client';

import clsx from 'clsx';
import { useState } from 'react';

import CloseButton from '@hi7/assets/icon/close-button.svg';
import Globe from '@hi7/assets/icon/globe.svg';
import Menu from '@hi7/assets/icon/menu.svg';
import Logo from '@hi7/assets/logo/logo.svg';
import Link from '@hi7/components/Link';
import MenuLink from '@hi7/components/MenuLink/Mobile';
import { useI18n } from '@hi7/lib/i18n';
import LinkButton from '../Form/LinkButton';
import LocaleSwitcher from '../LocaleSwitcher/Mobile';
import { ROUTES } from './config';

const HeaderMobile = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [localeOpen, setLocaleOpen] = useState(false);

  const toggleLocale = () => {
    setMenuOpen(false);
    setLocaleOpen((prev) => !prev);
  }

  const toggleMenu = () => {
    setLocaleOpen(false);
    setMenuOpen((prev) => !prev);
  }

  const t = useI18n();

  const closePanel = () => {
    setLocaleOpen(false);
    setMenuOpen(false);
  };

  const open = menuOpen || localeOpen;

  const buttonChild = ROUTES.find((s) => s.asButton);

  return (
    <>
      <div className="h-14"></div>
      <nav
        className={clsx(
          'm-h-14 fixed top-0 z-30 w-full transform overflow-auto transition-all duration-200 ',
          open ? 'bg-white text-[#1E1E1E] px-0 py-0' : 'bg-linear-[124.78deg,#33B2ED_0.54%,#2243EA] text-white px-4 py-2.5 ',
        )}
      >
        <div className={clsx('flex items-center gap-4', open ? 'bg-linear-[124.78deg,#33B2ED_0.54%,#2243EA] px-4 py-2.5' : 'bg-transparent')}>
          <Link url={''} className="-ml-5" onClick={closePanel}>
            <Logo height={36} className="scale-65" />
          </Link>
          <div className="flex-1" />

          {buttonChild && (
            <LinkButton
              target="_blank"
              type="main"
              size="S"
              url={buttonChild.url}
              onClick={closePanel}
              className="-ml-5"
            >
              {t(buttonChild.children as string)}
            </LinkButton>
          )}
          {!open && <Globe onClick={toggleLocale} />}

          {!open && <Menu className="mb-1" width={20} height={20} onClick={toggleMenu} />}

          {open && <><CloseButton className="scale-85" onClick={closePanel} /></>}
        </div>

        {menuOpen && (
          <div className="flex flex-col px-10 pt-3 pb-7 font-[400] text-[24px] leading-[30px] h-screen">
            <video
              autoPlay
              muted
              loop
              playsInline
              className="absolute top-0 left-0 h-full w-full object-cover z-[-1] opacity-30"
              src="/videos/header-bg.mp4"
            />
            {ROUTES.filter((s) => !s.asButton).map((route, index) => (
              <MenuLink
                key={index}
                {...route}
                onClick={() => {
                  toggleMenu();
                  // route.onClick?.();
                }}
              />
            ))}
          </div>
        )}

        {localeOpen && (
          <>
            <video
              autoPlay
              muted
              loop
              playsInline
              className="absolute top-0 left-0 h-full w-full object-cover z-[-1] opacity-30"
              src="/videos/header-bg.mp4"
            />
            <div className="mt-6 justify-items-start px-10 font-[400] text-[24px] h-screen">
              <LocaleSwitcher />
            </div>
          </>
        )}
      </nav>
    </>
  );
};

export default HeaderMobile;
