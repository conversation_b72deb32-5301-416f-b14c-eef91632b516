'use client';

import CircleElement from '@hi7/assets/background/circle-element1.png';
import CircleElement2 from '@hi7/assets/background/circle-element2.png';
import VerticalCircuit from '@hi7/assets/background/vertical-circuit.png';
import AnimationFrame from '@hi7/components/AnimationFrame';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import { Archivo, Archivo_Black } from 'next/font/google';
import Image from 'next/image';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

const archivoBlack = Archivo_Black({
  subsets: ['latin'],
  weight: ['400'],
  display: 'swap',
});
function Empowering() {

  const t = useI18n();
  const locale = useLocale();

  const features = [
    {
      title: 'Cloud Office Monitoring - Efficient Management',
      description: 'CloudSeven Cloud Office Monitoring offers enterprises an intelligent computer monitoring solution with real-time tracking, behaviour analysis and attendance management to boost productivity and team collaboration.',
    },
    {
      title: 'Cloud Desktop - Global Expansion',
      description: 'CloudSeven Cloud Desktop provides enterprises with a secure and private digital workspace for global operations. It supports seamless multi-device remote access, catering to diverse international business needs and enabling fast, cross-regional enterprise growth.',
    },
  ]

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) overflow-hidden bg-linear-[0deg,#B5E1FF_0.54%,#fff] pb-[50px] md:content-center md:pb-0 lg:min-h-(--min-h-hvh) lg:h-full lg:pt-[78px] lg:pb-[40px] xl:min-h-[115vh] 2xl:min-h-screen">

          <AnimationFrame
            variant="FadeIn"
            once={false}
            className=""
          >
            <Image
              src={VerticalCircuit}
              className="hidden invert brightness-0 grayscale absolute inset-0 w-full h-full object-cover bg-no-repeat bg-center scale-105 opacity-60 xl:block"
              alt="Background"
            />
          </AnimationFrame>

          <div className="relative flex flex-col items-center justify-center px-5 py-11 lg:pt-[60px] xl:pt-[20px]">
            <div className="w-[100%] lg:w-[80dvw] lg:min-w-[855px] xl:w-[90dvw] xl:min-w-[1200px] 2xl:w-[80vw]">
              <AnimationFrame
                variant="SlideUpHomeSection4"
                once={false}
                className="xl:flex-col"
              >
                <h1 className="relative z-10 mb-[14px] flex flex-col leading-none xl:px-10 xl:mb-[0px]">
                  <span className={clsx(
                    archivoBlack.className,
                    'bg-gradient-to-r from-[#3D5AEC] via-[#33B2ED] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text text-[42px] uppercase',
                    'lg:inline-block lg:text-[78px] ',
                    'xl:text-[64px] ',
                    '2xl:text-[80px]'
                  )}>
                    {t('Empowering')}
                  </span>
                </h1>
                <h3 className="text-[#000] mb-5 text-left text-[28px] font-[600] leading-none lg:text-[48px] xl:px-10">
                  {t('Global Business Expansion')}
                </h3>
              </AnimationFrame>

              <AnimationFrame
                variant="SlideDownSlightly"
                once={false}
                className=""
              >
                <Image src={CircleElement} alt='circle element background' className="hidden xl:flex absolute -right-10 top-0 opacity-100 2xl:-right-50 2xl:top-30" />
              </AnimationFrame>
              <AnimationFrame
                variant="FadeInHomeSection4Card"
                once={false}
                className=""
              >
                <div className="flex flex-col md:grid md:grid-cols-2 md:justify-self-center items-center justify-center gap-5 mt-10 md:gap-10 lg:gap-20 xl:gap-10 xl:px-10 xl:flex xl:flex-row xl:flex-nowrap 2xl:gap-[70px]">
                  {features.map((feature, idx) => (
                    <>
                      <div
                        key={`pkg1-${idx}`}
                        className={clsx(
                          'z-[1] min-h-[330px] h-full w-full mb-5 relative flex flex-col gap-4 rounded-[15px] bg-[linear-gradient(135deg,_rgba(255,255,255,0.6)_0%,_rgba(255,255,255,0.1)_100%)] px-[20px] shadow-[0px_4px_24px_-1px_rgba(0,0,0,0.2)] text-center py-[20px] xl:py-[0px] md:px-[30px] text-[#1E1E1E]',
                          'lg:w-full',
                          'xl:min-w-[160px] xl:min-h-[280px] xl:w-full xl:h-full xl:justify-between',
                          '2xl:h-[330px] 2xl:w-full 2xl:min-w-[340px] 2xl:justify-between'
                        )}
                      >
                        <div className={`relative flex flex-col gap-4 flex-grow 2xl:gap-0 xl:mt-0 justify-center`}>
                          <b className={`text-[24px] font-[600] leading-[1.2] text-[#33B2ED] xl:text-[36px] xl:pt-0 2xl:pt-5 text-left`}>
                            {t(feature.title)}
                          </b>
                          <div className={`flex flex-col gap-2.5 text-[16px] font-[400] leading-[1.4] tracking-normal text-left xl:mt-3 2xl:mt-5`}>
                            {
                              <span>
                                {t(feature.description)}
                              </span>
                            }
                          </div>
                        </div>
                      </div>
                    </>
                  ))}
                </div>
              </AnimationFrame>
              <AnimationFrame
                variant="SlideUpSlightly"
                once={false}
                className=""
              >
                <Image src={CircleElement2} alt="Circle Element" className="hidden xl:flex absolute left-[-10%] -top-40 scale-90 opacity-100 z-[0] 2xl:scale-130 2xl:left-[-8%] 2xl:top-[30%]" />
              </AnimationFrame>
            </div>
          </div>
        </div>
      </div>
    </div >
  );
}

export default Empowering;
