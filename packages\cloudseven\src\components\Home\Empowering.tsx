'use client';

import CircleElement from '@hi7/assets/background/circle-element1.png';
import CircleElement2 from '@hi7/assets/background/circle-element2.png';
import VerticalCircuit from '@hi7/assets/background/vertical-circuit.png';
import AnimationFrame from '@hi7/components/AnimationFrame';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import { Archivo, Archivo_Black } from 'next/font/google';
import Image from 'next/image';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

const archivoBlack = Archivo_Black({
  subsets: ['latin'],
  weight: ['400'],
  display: 'swap',
});
function Empowering() {
  const t = useI18n();
  const locale = useLocale();

  const features = [
    {
      title: 'Cloud Office Monitoring - Efficient Management',
      description:
        'CloudSeven Cloud Office Monitoring offers enterprises an intelligent computer monitoring solution with real-time tracking, behaviour analysis and attendance management to boost productivity and team collaboration.',
    },
    {
      title: 'Cloud Desktop - Global Expansion',
      description:
        'CloudSeven Cloud Desktop provides enterprises with a secure and private digital workspace for global operations. It supports seamless multi-device remote access, catering to diverse international business needs and enabling fast, cross-regional enterprise growth.',
    },
  ];

  return (
    <div
      className="flex min-h-screen items-center justify-center overflow-hidden"
      data-scroll-section
    >
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) overflow-hidden bg-linear-[0deg,#B5E1FF_0.54%,#fff] pb-[50px] md:content-center md:pb-0 lg:h-full lg:min-h-(--min-h-hvh) lg:pt-[78px] lg:pb-[40px] xl:min-h-[115vh] 2xl:min-h-screen">
          <AnimationFrame variant="FadeIn" once={false} className="">
            <Image
              src={VerticalCircuit}
              className="absolute inset-0 hidden h-full w-full scale-105 bg-center bg-no-repeat object-cover opacity-60 brightness-0 grayscale invert xl:block"
              alt="Background"
            />
          </AnimationFrame>

          <div className="relative flex flex-col items-center justify-center px-5 py-11 lg:pt-[60px] xl:pt-[20px]">
            <div className="w-[100%] lg:w-[80dvw] lg:min-w-[855px] xl:w-[90dvw] xl:min-w-[1200px] 2xl:w-[80vw]">
              <AnimationFrame
                variant="SlideUpHomeSection4"
                once={false}
                className="xl:flex-col"
              >
                <h1 className="relative z-10 mb-[14px] flex flex-col leading-none xl:mb-[0px] xl:px-10">
                  <span
                    className={clsx(
                      archivoBlack.className,
                      'inline-block bg-gradient-to-r from-[#3D5AEC] via-[#33B2ED] to-[#33B2ED] bg-clip-text text-[42px] font-[900] text-transparent uppercase',
                      'lg:inline-block lg:text-[78px]',
                      'xl:text-[64px]',
                      '2xl:text-[80px]',
                    )}
                  >
                    {t('Empowering')}
                  </span>
                </h1>
                <h3 className="mb-5 text-left text-[28px] leading-none font-[600] text-[#000] lg:text-[48px] xl:px-10">
                  {t('Global Business Expansion')}
                </h3>
              </AnimationFrame>

              <AnimationFrame
                variant="SlideDownSlightly"
                once={false}
                className=""
              >
                <Image
                  src={CircleElement}
                  alt="circle element background"
                  className="absolute top-0 -right-10 hidden opacity-100 xl:flex 2xl:top-30 2xl:-right-50"
                />
              </AnimationFrame>
              <AnimationFrame
                variant="FadeInHomeSection4Card"
                once={false}
                className=""
              >
                <div className="mt-10 flex flex-col items-center justify-center gap-5 md:grid md:grid-cols-2 md:gap-10 md:justify-self-center lg:gap-20 xl:flex xl:flex-row xl:flex-nowrap xl:gap-10 xl:px-10 2xl:gap-[70px]">
                  {features.map((feature, idx) => (
                    <>
                      <div
                        key={`pkg1-${idx}`}
                        className={clsx(
                          'relative z-[1] mb-5 flex h-full min-h-[330px] w-full flex-col gap-4 rounded-[15px] bg-[linear-gradient(135deg,_rgba(255,255,255,0.6)_0%,_rgba(255,255,255,0.1)_100%)] px-[20px] py-[20px] text-center text-[#1E1E1E] shadow-[0px_4px_24px_-1px_rgba(0,0,0,0.2)] md:px-[30px] xl:py-[0px]',
                          'lg:w-full',
                          'xl:h-full xl:min-h-[280px] xl:w-full xl:min-w-[160px] xl:justify-between',
                          '2xl:h-[330px] 2xl:w-full 2xl:min-w-[340px] 2xl:justify-between',
                        )}
                      >
                        <div
                          className={`relative flex flex-grow flex-col justify-center gap-4 xl:mt-0 2xl:gap-0`}
                        >
                          <b
                            className={`text-left text-[24px] leading-[1.2] font-[600] text-[#33B2ED] xl:pt-0 xl:text-[36px] 2xl:pt-5`}
                          >
                            {t(feature.title)}
                          </b>
                          <div
                            className={`flex flex-col gap-2.5 text-left text-[16px] leading-[1.4] font-[400] tracking-normal xl:mt-3 2xl:mt-5`}
                          >
                            {<span>{t(feature.description)}</span>}
                          </div>
                        </div>
                      </div>
                    </>
                  ))}
                </div>
              </AnimationFrame>
              <AnimationFrame
                variant="SlideUpSlightly"
                once={false}
                className=""
              >
                <Image
                  src={CircleElement2}
                  alt="Circle Element"
                  className="absolute -top-40 left-[-10%] z-[0] hidden scale-90 opacity-100 xl:flex 2xl:top-[30%] 2xl:left-[-8%] 2xl:scale-130"
                />
              </AnimationFrame>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Empowering;
