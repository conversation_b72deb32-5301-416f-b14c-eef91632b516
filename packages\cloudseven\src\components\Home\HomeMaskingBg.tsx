'use client';

import CircleElement from '@hi7/assets/background/circle-element1.png';
import CircleElement2 from '@hi7/assets/background/circle-element2.png';
import HomeCloudBgMobile from '@hi7/assets/background/home-cloud-background-mobile.png';
import HomeCloudBg from '@hi7/assets/background/home-cloud-background.png';
import AnimationFrame from '@hi7/components/AnimationFrame';
import useScreenSize from '@hi7/helpers/useScreenSize';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import { motion } from 'framer-motion';
import { Archivo } from 'next/font/google';
import Image from 'next/image';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

function HomeMaskingBg() {
  const t = useI18n();
  const locale = useLocale();
  const { isMobile } = useScreenSize();

  return (
    <div className="flex items-center justify-center">
      <div className="w-full">
        <div className="bg-[linear-gradient(180deg,_#FFFFFF,_#B5E1FF_50%)] relative min-h-[100vh] h-screen pb-[50px] md:content-center md:pb-0 md:min-h-[70vh] lg:h-full lg:pb-[45px] xl:min-h-screen xl:h-screen 2xl:min-h-screen 2xl:w-screen">
          <Image
            src={isMobile ? HomeCloudBgMobile : HomeCloudBg}
            alt={'home-cloud-background-alt'}
            fill
            className='object-cover w-full h-full opacity-100 object-bottom'
          />
          <AnimationFrame
            variant="SlideDownHomeHero"
            once={true}
            className=""
          >
            <Image src={CircleElement} alt='circle element background' className="hidden xl:flex xl:absolute -right-[10%] scale-70" />
          </AnimationFrame>
          <AnimationFrame
            variant="FadeIn"
            once={true}
            className="relative z-100"
          >
            <div className="translate-y-[30%] md:translate-y-[50%] xl:translate-y-[35%] 2xl:translate-y-[65%] 2xl:w-screen">
              <h3 className="text-[#000] mb-5 px-0 text-center text-[28px] font-[600] leading-none lg:text-[42px] xl:px-10">
                {t('Your Global Remote Work')}
              </h3>
              <h1 className="flex flex-col leading-none mx-auto text-center">
                <span className={clsx(
                  archivo.className,
                  'bg-gradient-to-r from-[#3D5AEC] via-[#3D5AEC] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text text-[42px] uppercase mb-5',
                  'lg:inline-block lg:text-[78px] ',
                  'xl:text-[70px] xl:w-[70%] xl:mx-auto xl:mb-2',
                  '2xl:text-[80px]'
                )}>
                  {t('Monitoring Assistant')}
                </span>
              </h1>
              <p className="w-[90%] mx-auto text-[16px] font-[400] text-black text-center mb-5 md:w-[80%] xl:mb-3 xl:w-[80%] 2xl:w-[70%]">
                {t('Secure your business with a private remote workspace. Our smart monitoring system prevents data leaks and fraud while enhancing efficiency—your all-in-one enterprise solution!')}
              </p>
              <div className='flex items-center justify-center'>
                <button className="mb-5 bg-linear-[135deg,#2243EA_0%,#2243EA_40%,#33B2ED] font-[500] text-[14px] text-white mx-auto items-center justify-center px-6 py-2 rounded-md font-medium hover:bg-opacity-90 transition">
                  {t('Try For Free')}
                </button>
              </div>
            </div>
          </AnimationFrame>
          <motion.div
            className="relative z-10 absolute bottom-[100px] right-1/2 w-full h-[100vh] flex items-end justify-center scale-[30%] md:scale-[55%] md:-bottom-1/6 md:right-[27%] lg:-bottom-[200px] xl:scale-[50%] xl:bottom-[120px] xl:left-[-5%] 2xl:left-[7%] 2xl:-bottom-20 2xl:scale-[75%]"
            initial={{ y: -300, opacity: 0, scale: 2 }}
            animate={{ y: 50, opacity: 1, scale: 1 }}
            transition={{ duration: 2, ease: "easeOut" }}
          >
            <svg viewBox="0 0 1200 600" width="0%" height="0%" className="absolute" preserveAspectRatio="xMidYMid meet">
              <defs>
                <clipPath id="customShape1" clipPathUnits="userSpaceOnUse">
                  <path d="M1355.74 329.636C1316.26 315.367 1274.3 306.412 1230.71 303.464C1215.74 262.788 1195.16 224.823 1170.04 190.406C1085.83 75.0935 949.839 0 796.606 0C643.374 0 507.741 74.8727 423.522 189.917C427.331 189.838 431.109 189.633 434.95 189.633C498.973 189.633 560.587 200.181 618.14 219.62C668.168 182.744 729.876 160.908 796.622 160.908C869.35 160.908 936.143 186.811 988.312 229.884C1019.21 255.393 1044.92 286.957 1063.73 322.698C1085.85 364.699 1098.43 412.47 1098.43 463.174C1098.43 469.102 1098.21 474.982 1097.88 480.831C1129.58 469.464 1163.66 463.174 1199.22 463.174C1219.66 463.174 1239.63 465.239 1258.93 469.133C1299.07 477.237 1336.35 493.35 1369.05 515.722C1404.36 539.891 1434.19 571.423 1456.57 608.016H1634.03C1587.26 478.892 1484.81 376.287 1355.78 329.651L1355.74 329.636Z" />
                  <path d="M740.785 418.398C711.364 392.352 678.589 370.059 643.264 352.117C609.261 334.838 572.866 321.689 534.707 313.239C502.562 306.112 469.189 302.265 434.935 302.265C409.7 302.265 384.97 304.378 360.822 308.288C315.752 315.588 272.84 329.399 233.107 348.775C125.511 401.26 41.1495 494.343 0 608H177.475C191.469 585.171 208.376 564.312 227.707 545.976C258.074 517.187 294.375 494.642 334.58 480.405C366.001 469.275 399.751 463.157 434.935 463.157C455.494 463.157 475.549 465.317 494.959 469.259C540.406 478.498 582.154 497.969 617.73 525.212C633.158 537.021 647.467 550.217 660.36 564.738C672.261 578.139 682.934 592.628 692.332 607.984H869.775C856.489 571.249 838.716 536.643 817.055 504.874C795.284 472.948 769.609 443.907 740.77 418.382L740.785 418.398Z" />
                </clipPath>
              </defs>
            </svg>
            <div
              className="relative w-full h-full lg:scale-128 lg:translate-x-5 xl:scale-100 xl:translate-x-0"
              style={{
                clipPath: "url(#customShape1)",
                overflow: "visible",
              }}
            >
              <video
                autoPlay
                muted
                loop
                playsInline
                className="object-cover w-full h-full scale-[1000%] md:scale-350 xl:scale-180"
                src="/videos/home-landing.mp4"
              />
            </div>
          </motion.div>
          <Image src={CircleElement2} alt="Circle Element" className="hidden xl:flex absolute left-[0%] top-1/3 scale-105 opacity-100" />
          <AnimationFrame
            variant="HomeHeroMoveRightRotate"
            once={true}
            className="absolute left-[-10%] top-1/3 scale-105 opacity-100"
          >
            <Image src={CircleElement2} alt="Circle Element" className="hidden xl:flex " />
          </AnimationFrame>
        </div>
      </div>
    </div>
  );
}

export default HomeMaskingBg; 0