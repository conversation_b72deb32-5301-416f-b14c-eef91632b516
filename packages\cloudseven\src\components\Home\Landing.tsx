'use client';

import useScreenSize from '@hi7/helpers/useScreenSize';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import { AnimatePresence, motion } from 'framer-motion';
import { Archivo } from 'next/font/google';
import { useEffect, useRef, useState } from 'react';
import HomeMaskingBg from './HomeMaskingBg';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

function Landing() {
  const locale = useLocale();
  const t = useI18n();
  const { isMobile } = useScreenSize();

  const sectionRef = useRef<HTMLDivElement>(null);
  const [hasAnimated, setHasAnimated] = useState(false);
  const [showAnimation, setShowAnimation] = useState(false);

  useEffect(() => {
    const node = sectionRef.current;
    // If the animation has already played, do nothing.
    if (!node || hasAnimated) return;

    const handleWheel = (e: WheelEvent) => {
      const isScrollingDown = e.deltaY > 0;

      // Only trigger the animation on the first scroll down.
      if (isScrollingDown) {
        e.preventDefault();
        document.body.dataset.scrollLock = 'true';

        // Trigger the original AnimatePresence animation
        setShowAnimation(true);
        setHasAnimated(true);

        // Unlock scroll after animation duration
        setTimeout(() => {
          delete document.body.dataset.scrollLock;
        }, 1000); // Match your original animation duration
      } else {
        // If scrolling up before the animation, let the scroll snap handle it.
        delete document.body.dataset.scrollLock;
      }
    };

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          window.addEventListener('wheel', handleWheel, { passive: false });
        } else {
          window.removeEventListener('wheel', handleWheel);
        }
      },
      { threshold: 0.8 },
    );

    observer.observe(node);

    return () => {
      if (node) observer.unobserve(node);
      window.removeEventListener('wheel', handleWheel);
    };
  }, [hasAnimated]);

  return (
    <div
      ref={sectionRef}
      className="flex items-center justify-center overflow-hidden"
      data-scroll-section
    >
      <div className="w-full">
        <div className="relative flex h-screen items-center overflow-hidden bg-white">
          {/* Background video - always visible (first page) */}
          <video
            autoPlay
            muted
            loop
            playsInline
            className="absolute inset-0 h-full w-full object-cover"
            src="/videos/home-landing.mp4"
          />

          {/* Original AnimatePresence masking animation */}
          <AnimatePresence>
            {showAnimation && (
              <motion.div
                className="absolute inset-0 z-10"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1.0 }}
              >
                <HomeMaskingBg />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}

export default Landing;
