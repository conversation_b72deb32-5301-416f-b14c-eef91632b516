'use client';

import useScreenSize from '@hi7/helpers/useScreenSize';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import { AnimatePresence, motion } from 'framer-motion';
import { Archivo } from 'next/font/google';
import { useEffect, useState } from 'react';
import HomeMaskingBg from './HomeMaskingBg';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

function Landing() {
  const locale = useLocale();
  const t = useI18n();
  const { isMobile } = useScreenSize();

  const [showMasking, setShowMasking] = useState(false);
  const [scrollLocked, setScrollLocked] = useState(false);
  const [countdown, setCountdown] = useState(3); // 3 second countdown
  const [showAnimation, setShowAnimation] = useState(false);

  // Countdown timer
  useEffect(() => {
    if (showMasking) {
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setShowAnimation(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [showMasking]);

  // Wheel listener for triggering animation
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      if (!showMasking && e.deltaY > 0) {
        // Only trigger on scroll down from the landing section
        const landingSection = document.querySelector('[data-scroll-section]');
        if (landingSection) {
          const rect = landingSection.getBoundingClientRect();
          const isLandingInView =
            rect.top <= 100 && rect.bottom >= window.innerHeight * 0.5;

          if (isLandingInView) {
            e.preventDefault();
            setShowMasking(true);
            setScrollLocked(true);
          }
        }
      }
    };

    window.addEventListener('wheel', handleWheel, { passive: false });
    return () => window.removeEventListener('wheel', handleWheel);
  }, [showMasking]);

  // Scroll lock management
  useEffect(() => {
    document.body.style.overflow = scrollLocked ? 'hidden' : '';
    document.body.dataset.scrollLock = scrollLocked ? 'true' : 'false';
    return () => {
      document.body.style.overflow = '';
      document.body.dataset.scrollLock = 'false';
    };
  }, [scrollLocked]);

  // Unlock scroll after animation completes
  useEffect(() => {
    if (showAnimation) {
      const timeout = setTimeout(() => {
        setScrollLocked(false);
      }, 3000); // Match to animation duration

      return () => clearTimeout(timeout);
    }
  }, [showAnimation]);

  return (
    <div
      className="flex items-center justify-center overflow-hidden"
      data-scroll-section
    >
      <div className="w-full">
        <div className="relative flex h-screen items-center overflow-hidden bg-white">
          {/* Background video */}
          <video
            autoPlay
            muted
            loop
            playsInline
            className="absolute inset-0 h-full w-full object-cover"
            src="/videos/home-landing.mp4"
          />

          {/* Countdown display */}
          {showMasking && !showAnimation && (
            <div className="absolute inset-0 z-20 flex items-center justify-center">
              <div className="text-8xl font-bold text-white">{countdown}</div>
            </div>
          )}

          {/* Masking animation */}
          <AnimatePresence>
            {showAnimation && (
              <motion.div
                className="absolute inset-0 z-10"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1.0 }}
              >
                <HomeMaskingBg />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}

export default Landing;
