'use client';

import useScreenSize from '@hi7/helpers/useScreenSize';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import { motion, useAnimation } from 'framer-motion';
import { Archivo } from 'next/font/google';
import { useEffect, useRef, useState } from 'react';
import HomeMaskingBg from './HomeMaskingBg';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

function Landing() {
  const locale = useLocale();
  const t = useI18n();
  const { isMobile } = useScreenSize();

  const sectionRef = useRef<HTMLDivElement>(null);
  const controls = useAnimation();
  const [hasAnimated, setHasAnimated] = useState(false);

  useEffect(() => {
    const node = sectionRef.current;
    // If the animation has already played, do nothing.
    if (!node || hasAnimated) return;

    const handleWheel = (e: WheelEvent) => {
      const isScrollingDown = e.deltaY > 0;

      // Only trigger the animation on the first scroll down.
      if (isScrollingDown) {
        e.preventDefault();
        document.body.dataset.scrollLock = 'true';

        controls
          .start({
            clipPath: 'circle(0% at 50% 50%)',
            transition: { duration: 1.2, ease: [0.76, 0, 0.24, 1] },
          })
          .then(() => {
            setHasAnimated(true); // This triggers a re-render, removing the top layer.
            delete document.body.dataset.scrollLock;
          });
      } else {
        // If scrolling up before the animation, let the scroll snap handle it.
        delete document.body.dataset.scrollLock;
      }
    };

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          window.addEventListener('wheel', handleWheel, { passive: false });
        } else {
          window.removeEventListener('wheel', handleWheel);
        }
      },
      { threshold: 0.8 },
    );

    observer.observe(node);

    return () => {
      if (node) observer.unobserve(node);
      window.removeEventListener('wheel', handleWheel);
    };
  }, [controls, hasAnimated]);

  return (
    <div
      ref={sectionRef}
      className="relative h-screen w-full"
      data-scroll-section
    >
      {/* This is the bottom layer (second page) - HomeMaskingBg that gets revealed */}
      <HomeMaskingBg />

      {/* This is the top layer (first page) - Video that animates away with circular mask */}
      {!hasAnimated && (
        <motion.div
          className="absolute inset-0 z-10"
          initial={{ clipPath: 'circle(150% at 50% 50%)' }}
          animate={controls}
        >
          <div className="relative flex h-screen w-full items-center overflow-hidden bg-white">
            <video
              autoPlay
              muted
              loop
              playsInline
              className="absolute inset-0 h-full w-full object-cover"
              src="/videos/home-landing.mp4"
            />
          </div>
        </motion.div>
      )}
    </div>
  );
}

export default Landing;
