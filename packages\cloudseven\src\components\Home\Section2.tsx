'use client';

import LongCircuitElement from '@hi7/assets/background/long-circuit-element.png';
import MosaicCube from '@hi7/assets/background/mosaic-cube.png';
import CloudDatabase from '@hi7/assets/icon/cloud-database.png';
import CloudDevice from '@hi7/assets/icon/cloud-device.png';
import ShieldLock from '@hi7/assets/icon/shield-lock.png';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import { Archivo, Archivo_Black } from 'next/font/google';
import Image from 'next/image';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});
const archivoBlack = Archivo_Black({
  subsets: ['latin'],
  weight: ['400'],
  display: 'swap',
});

function Section2() {
  const t = useI18n();
  const locale = useLocale();

  const features = [
    {
      bigText: '50+',
      smallText: 'Data Centres Worldwide',
      image: CloudDatabase,
    },
    {
      bigText: '100%',
      smallText: 'Business Risks Reduced',
      image: ShieldLock,
    },
    {
      bigText: '100,000+',
      smallText: 'Devices Monitored Daily',
      image: CloudDevice,
    },
  ];

  return (
    <div
      className="flex items-center justify-center overflow-hidden"
      data-scroll-section
    >
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) overflow-hidden bg-[linear-gradient(180deg,_#FFFFFF_,_#B5E1FF_40%,_#B5E1FF_)] pb-[50px] md:min-h-[70vh] md:content-center md:pb-0 lg:h-full lg:pb-[45px] xl:min-h-screen xl:pt-[45px] 2xl:min-h-screen">
          <Image
            src={MosaicCube}
            alt="Circuit Element"
            className="absolute -top-5 -right-15 z-0 scale-70 opacity-70 brightness-0 grayscale invert md:top-5 lg:top-5 lg:scale-110 xl:-top-15 xl:scale-80 2xl:right-10 2xl:scale-110"
          />
          <div className="relative flex flex-col items-center justify-center px-5 py-11 lg:pt-[60px] xl:pt-[20px]">
            <div className="w-[100%] lg:w-[80dvw] lg:min-w-[855px] xl:w-[90dvw] xl:min-w-[1200px]">
              <div className="text-center">
                <h1 className="mx-auto mb-[14px] inline-block align-top leading-none">
                  <span
                    className={clsx(
                      archivo.className,
                      'bg-gradient-to-r from-[#3D5AEC] to-[#33B2ED] bg-clip-text text-[42px] font-[900] text-transparent uppercase',
                      'lg:inline-block lg:text-[78px]',
                      'xl:text-[96px]',
                      '2xl:text-[80px]',
                    )}
                  >
                    {t('built')}
                  </span>
                </h1>

                <h3
                  className={`mb-5 inline-block px-10 align-bottom text-[28px] leading-none font-[600] text-[#000] md:px-3 lg:text-[48px]`}
                >
                  {t('to Scale Your Business')}
                </h3>
              </div>
              <div
                className={clsx(
                  'flex flex-col items-center justify-center gap-5',
                  'md:mt-10 md:grid md:grid-cols-2 md:gap-10 md:justify-self-center',
                  'xl:mt-5 xl:flex xl:flex-row xl:flex-nowrap xl:gap-[50px] xl:px-10',
                  '2xl:gap-[80px]',
                )}
              >
                {features.map((feature, idx) => (
                  <div
                    key={`pkg1-${idx}`}
                    className="mb-3 flex flex-row items-center justify-center xl:flex-col"
                  >
                    <div className="relative z-10 -mr-[40px] h-[150px] w-[150px] lg:h-[200px] lg:w-[200px] xl:mr-0 xl:h-[180px] xl:w-[180px] 2xl:h-[210px] 2xl:w-[210px]">
                      <Image
                        src={feature.image}
                        alt="feature image"
                        className="object-contain"
                        fill
                      />
                    </div>

                    <div
                      className={clsx(
                        'relative flex h-full min-h-[150px] w-[65%] flex-col justify-center rounded-[15px] bg-[linear-gradient(135deg,_rgba(255,255,255,0.6)_0%,_rgba(255,255,255,0.1)_100%)] py-[20px] pl-[35px] text-[#3D5AEC] shadow-[0px_4px_24px_-1px_rgba(0,0,0,0.2)]',
                        'lg:min-h-[180px] lg:w-[240px]',
                        'xl:transfrom xl:min-h-[300px] xl:w-[280px] xl:translate-y-[-35%] xl:px-3 xl:py-[0px]',
                        'xl:min-h-[320px] 2xl:w-[300px]',
                      )}
                    >
                      <div className="w-[80%] translate-x-[15px] transform xl:mt-10 2xl:mt-15">
                        <b
                          className={`block leading-none ${archivoBlack.className} ${idx === features.length - 1 ? 'text-[30px] lg:text-[35px] xl:text-[40px] 2xl:text-[45px]' : 'text-[45px] lg:text-[50px] xl:text-[55px] 2xl:text-[60px]'} mb-2`}
                        >
                          {t(feature.bigText)}
                        </b>

                        <div
                          className={`flex flex-col text-[18px] leading-tight font-[400] xl:text-[20px]`}
                        >
                          <span className="">{t(feature.smallText)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <Image
                src={LongCircuitElement}
                alt="Circuit Element"
                className="absolute -bottom-5 -left-25 scale-65 transform opacity-100 brightness-0 grayscale invert md:-bottom-10 xl:bottom-10 2xl:left-0 2xl:scale-110"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Section2;
