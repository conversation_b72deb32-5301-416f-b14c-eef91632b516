'use client';
import Section3BgImg from '@hi7/assets/background/home-section-3-bg.png';
import Attendance from '@hi7/assets/icon/attendance.svg';
import Cost from '@hi7/assets/icon/cost.svg';
import Data from '@hi7/assets/icon/data.svg';
import Inefficient from '@hi7/assets/icon/inefficient.svg';
import Image1 from '@hi7/assets/icon/p1.png';
import Image2 from '@hi7/assets/icon/p2.png';
import Image3 from '@hi7/assets/icon/p3.png';
import Image4 from '@hi7/assets/icon/p4.png';
import Image5 from '@hi7/assets/icon/p5.png';
import Image6 from '@hi7/assets/icon/p6.png';
import Sleep from '@hi7/assets/icon/sleep.svg';
import Work from '@hi7/assets/icon/work.svg';
import useScreenSize from '@hi7/helpers/useScreenSize';
import { useI18n } from '@hi7/lib/i18n';
import useEmblaCarousel from 'embla-carousel-react';
import { AnimatePresence, motion } from 'framer-motion';
import { Archivo, Archivo_Black } from 'next/font/google';
import Image from 'next/image';
import { useCallback, useEffect, useRef, useState } from 'react';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

const archivoBlack = Archivo_Black({
  subsets: ['latin'],
  weight: ['400'],
  display: 'swap',
});

export default function ChallengesSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const sectionRef = useRef<HTMLDivElement>(null);
  const isScrollingRef = useRef(false);
  const t = useI18n();

  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    align: 'center',
  });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
    goToSlide(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;

    const originalArrayList = emblaApi.scrollSnapList();
    const filteredArrayList = Array.from(new Set(originalArrayList));
    setScrollSnaps(filteredArrayList);
    emblaApi.on('select', onSelect);
    onSelect();
  }, [emblaApi, onSelect]);

  const scrollTo = useCallback(
    (index: number) => {
      if (!emblaApi) return;
      emblaApi.scrollTo(index);
      setCurrentIndex(index);
    },
    [emblaApi],
  );

  const challenges = [
    {
      title: t('Low Employee Productivity'),
      description: t(
        'Inefficient employees lead to declining company performance, directly impacting business growth.',
      ),
      image: Image1,
      icon: Sleep,
    },
    {
      title: 'Employees Doing Personal Work',
      description:
        'Lack of monitoring over internet activity compromises company interests.',
      image: Image2,
      icon: Work,
    },
    {
      title: 'Risk of Data Breaches',
      description:
        'Important business data stored on local computers is vulnerable to loss or leaks.',
      image: Image3,
      icon: Data,
    },
    {
      title: 'Inaccurate Attendance Logs',
      description:
        'Traditional systems allow loopholes in clocking in, making work hours unreliable.',
      image: Image4,
      icon: Attendance,
    },
    {
      title: 'Inefficient Team Management',
      description:
        'Global teams struggle with real-time collaboration and communication.',
      image: Image5,
      icon: Inefficient,
    },
    {
      title: 'High Office Costs',
      description:
        'Expensive hardware and IT maintenance add to business expenses.',
      image: Image6,
      icon: Cost,
    },
  ];

  const nextSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === challenges.length - 1 ? 0 : prevIndex + 1,
    );
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? challenges.length - 1 : prevIndex - 1,
    );
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  useEffect(() => {
    const handleScroll = (e: WheelEvent) => {
      const { isMobile } = useScreenSize();
      if (isMobile) return;

      if (!sectionRef.current) return;

      const rect = sectionRef.current.getBoundingClientRect();
      const isInView = rect.top <= window.innerHeight && rect.bottom >= 0;

      if (!isInView) return;

      if (isScrollingRef.current) return;

      if (e.deltaY > 0) {
        if (currentIndex === challenges.length - 1) {
          // Allow scrolling to next section
          return;
        } else {
          e.preventDefault();
          isScrollingRef.current = true;
          // Set scroll lock to prevent global scroll snap from interfering
          document.body.dataset.scrollLock = 'true';
          setTimeout(() => {
            isScrollingRef.current = false;
            document.body.dataset.scrollLock = 'false';
          }, 500);
          nextSlide();
        }
      } else {
        if (currentIndex === 0) {
          // Allow scrolling to previous section
          return;
        } else {
          e.preventDefault();
          isScrollingRef.current = true;
          // Set scroll lock to prevent global scroll snap from interfering
          document.body.dataset.scrollLock = 'true';
          setTimeout(() => {
            isScrollingRef.current = false;
            document.body.dataset.scrollLock = 'false';
          }, 500);
          prevSlide();
        }
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (!sectionRef.current) return;

      const rect = sectionRef.current.getBoundingClientRect();
      const isInView = rect.top <= window.innerHeight && rect.bottom >= 0;

      if (!isInView) return;

      if (e.key === 'ArrowDown' || e.key === 'ArrowRight') {
        if (currentIndex < challenges.length - 1) {
          e.preventDefault();
          nextSlide();
        }
      } else if (e.key === 'ArrowUp' || e.key === 'ArrowLeft') {
        if (currentIndex > 0) {
          e.preventDefault();
          prevSlide();
        }
      }
    };

    window.addEventListener('wheel', handleScroll, { passive: false });
    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('wheel', handleScroll);
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const minSwipeDistance = 50;

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientY);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientY);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isUpSwipe = distance > minSwipeDistance;
    const isDownSwipe = distance < -minSwipeDistance;

    if (isUpSwipe && currentIndex < challenges.length - 1) {
      nextSlide();
    } else if (isDownSwipe && currentIndex > 0) {
      prevSlide();
    }
  };

  return (
    <div
      className="flex min-h-screen items-center justify-center overflow-hidden"
      data-scroll-section
    >
      <div className="w-full">
        <div
          ref={sectionRef}
          className="relative h-full min-h-(--min-sm-h-hvh) overflow-hidden bg-linear-[180deg,#B5E1FF_0.54%,#fff_70%,#fff] pb-[50px] md:content-center md:pb-0 lg:h-(--min-h-hvh) lg:pt-[78px] lg:pb-0 xl:min-h-[105vh]"
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={onTouchEnd}
        >
          <Image
            src={Section3BgImg}
            className="absolute top-0 left-0 h-full w-full bg-center bg-no-repeat object-cover"
            alt="Background"
          />

          <div className="relative flex flex-col items-center justify-center pt-20 lg:pt-[60px] xl:flex-row xl:items-end xl:pt-[20px] xl:pl-5">
            {/****** Title ******/}
            <div className="self-start xl:w-2/5 xl:pl-20">
              <h2 className="px-3 text-[28px] leading-[35px] font-[600] text-[#000] md:px-6 md:text-[35px] xl:mb-5 xl:px-0 xl:text-[48px] xl:leading-[55px]">
                {t('Challenges with global remote teams')}?
              </h2>

              {/****** Caption ******/}
              <div className="mt-4 text-left xl:w-[100%] 2xl:w-[70%]">
                {/****** Carousel for Mobile ******/}
                <div
                  className="embla my-5 w-full overflow-hidden xl:hidden"
                  ref={emblaRef}
                >
                  <div className="embla__container flex px-3 py-5 md:px-7">
                    {challenges.map((challenge, idx) => (
                      <div
                        key={`feature-${idx}`}
                        className="embla__slide mx-2 w-full flex-shrink-0 overflow-hidden rounded-[15px]"
                      >
                        <Image src={challenge.image} alt={challenge.title} />
                      </div>
                    ))}
                  </div>
                </div>

                <AnimatePresence mode="wait">
                  <motion.div
                    key={challenges[currentIndex].title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5, ease: 'easeInOut' }}
                  >
                    {/****** Icon ******/}
                    {(() => {
                      const Icon = challenges[currentIndex].icon;
                      return (
                        <Icon className="mb-2 w-full translate-x-3 transform md:px-3 xl:translate-x-0 xl:px-0" />
                      );
                    })()}

                    {/****** Description ******/}
                    <h3 className="mb-3 px-5 text-[20px] font-[600] text-[#33B2ED] md:px-8 xl:px-0 xl:text-[24px]">
                      {challenges[currentIndex].title}
                    </h3>
                    <p className="px-5 text-[16px] font-[400] text-[#000] md:px-8 xl:px-0">
                      {challenges[currentIndex].description}
                    </p>
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>

            {/****** Carousel Indicators for Mobile ******/}
            <div className="mt-12 flex w-full justify-center px-2 xl:hidden">
              {scrollSnaps.map((_, index) => (
                <button
                  key={index}
                  onClick={() => scrollTo(index)}
                  className={`${archivoBlack.className} inline-block h-full w-full pb-2 text-[24px] text-[#3D5AEC] transition-colors ${index === selectedIndex ? 'border-b-2 border-[#3D5AEC]' : 'bg-transparent'}`}
                >
                  {' '}
                  0{index + 1}
                </button>
              ))}
            </div>

            {/****** Image Slider for Desktop ******/}
            <div className="relative hidden xl:block xl:w-3/5">
              <div className="relative h-[400px]">
                <PerspectiveCarousel
                  currentIndex={currentIndex}
                  setCurrentIndex={setCurrentIndex}
                  challenges={challenges}
                />
              </div>
            </div>
          </div>

          {/****** Indicators for Desktop ******/}
          <div className="z-10 mx-auto mt-4 hidden justify-center space-x-2 xl:flex xl:w-[80%] 2xl:w-[100%] 2xl:px-[5%]">
            {challenges.map((challenge, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`mr-5 flex h-[60px] w-full min-w-[160px] items-center justify-center text-left text-[#3D5AEC] transition-all duration-300 2xl:mr-8 2xl:h-[70px] ${
                  index === currentIndex
                    ? 'w-6 scale-108 border-b-2 border-[#3D5AEC]'
                    : 'w-2 border-none'
                }`}
              >
                <h1
                  className={`${archivoBlack.className} pr-2 text-[24px] 2xl:pr-3 2xl:text-[32px]`}
                >
                  0{index + 1}
                </h1>
                {currentIndex >= index && (
                  <h3 className="hidden text-[12px] leading-[12px] font-[400] xl:block 2xl:text-[15px] 2xl:leading-[16px]">
                    {challenge.title}
                  </h3>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

interface PerspectiveCarouselProps {
  currentIndex: number;
  setCurrentIndex: (index: number) => void;
  challenges: {
    title: string;
    description: string;
    image: any;
    icon: React.FC<React.SVGProps<SVGSVGElement>>;
  }[];
}

export const PerspectiveCarousel: React.FC<PerspectiveCarouselProps> = ({
  currentIndex,
  setCurrentIndex,
  challenges,
}) => {
  const cardVariants = {
    center: {
      x: 0,
      scale: 1,
      zIndex: 5,
      opacity: 1,
      filter: 'brightness(1)',
    },
    right: {
      x: '78%',
      scale: 0.5,
      zIndex: 4,
      opacity: 0.8,
      filter: 'brightness(0.8)',
    },
    hidden: {
      x: -500,
      scale: 0,
      opacity: 0,
      zIndex: 1,
    },
  };

  const getCardPosition = (index: number) => {
    if (index === currentIndex) return 'center';
    if (
      index === currentIndex + 1 ||
      (currentIndex === challenges.length - 1 && index === 0)
    ) {
      return 'right';
    }
    return 'hidden';
  };

  return (
    <div className="relative flex h-full w-full items-center justify-center overflow-hidden 2xl:h-full">
      {challenges.map((card, index) => (
        <motion.div
          key={card.title}
          variants={cardVariants}
          initial="hidden"
          animate={getCardPosition(index)}
          transition={{
            type: 'spring',
            stiffness: 200,
            damping: 90,
            duration: 2.5,
          }}
          className={`absolute h-full w-[73%] translate-x-[-15%] transform overflow-hidden rounded-xl bg-white shadow-lg ${index !== currentIndex ? 'top-[25%]' : ''}`}
          onClick={() => setCurrentIndex(index)}
        >
          <div className="relative h-full w-full">
            <Image
              src={card.image}
              alt={card.title}
              fill
              className="object-cover"
            />
          </div>
        </motion.div>
      ))}
    </div>
  );
};
