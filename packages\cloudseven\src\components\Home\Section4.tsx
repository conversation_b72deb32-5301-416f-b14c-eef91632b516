'use client';
import CloudDigital from '@hi7/assets/background/digital-world-cloud.jpg';
import ShakeHand from '@hi7/assets/background/handshake-with-glowing-network-connection.jpg';
import Silhouette from '@hi7/assets/background/silhouette-businessman-with-glasses.jpg';
import ContentBgImg1 from '@hi7/assets/background/why-choose-us-bg1.png';
import ContentBgImg2 from '@hi7/assets/background/why-choose-us-bg2.png';
import ContentBgImg3 from '@hi7/assets/background/why-choose-us-bg3.png';
import MobileCloudDigital from '@hi7/assets/background/why-choose-us-mobile-mask1.png';
import MobileShakeHand from '@hi7/assets/background/why-choose-us-mobile-mask2.png';
import MobileSilhouette from '@hi7/assets/background/why-choose-us-mobile-mask3.png';
import PointFormIcon from '@hi7/assets/icon/point-form-icon.svg';
import useScreenSize from '@hi7/helpers/useScreenSize';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import { AnimatePresence, motion } from 'framer-motion';
import { Archivo } from 'next/font/google';
import Image, { StaticImageData } from 'next/image';
import { useEffect, useState } from 'react';

type Feature = {
  title: string;
  description: string;
  image: StaticImageData;
  mobileImage: StaticImageData;
  points: string[];
  contentBackground: StaticImageData;
};

type Section4ViewProps = {
  className?: string;
  features?: Feature[];
};

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

function Section4() {
  const locale = useLocale();
  const { isMobile } = useScreenSize();

  const features = [
    {
      title: "Enterprise-Level Cloud Management",
      description: "Our advanced employee monitoring systems and enterprise-level cloud management interface help businesses achieve:",
      image: Silhouette,
      mobileImage: MobileSilhouette,
      points: [
        "Comprehensive Security: A private and reliable remote working environment.",
        "Intelligent Management: Effortless employee monitoring and workforce management.",
        "Seamless Collaboration: Overcoming geographic limitations for real-time teamwork.",
        "Productivity Boost: Enhancing efficiency and business growth."
      ],
      contentBackground: ContentBgImg3
    },
    {
      title: "10 Years of Expertise",
      description: "CloudSeven is a leading enterprise digital management platform. We provide high-performance cloud desktops for large-scale remote work and real-time employee monitoring to ensure data security.",
      image: CloudDigital,
      mobileImage: MobileCloudDigital,
      points: [],
      contentBackground: ContentBgImg1
    },
    {
      title: "Trusted by 50,000+ Businesses",
      description: "CloudSeven offers expert one-on-one support for seamless enterprise management. CloudSeven is a secure, efficient, and intelligent all-in-one platform for managing remote work.",
      image: ShakeHand,
      mobileImage: MobileShakeHand,
      points: [],
      contentBackground: ContentBgImg2
    },
  ];

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) bg-white overflow-hidden bg-[linear-gradient(180deg,_#FFFFFF,_#B5E1FF_50%,_#FFFFFF)] h-screen lg:min-h-[70vh] lg:pt-[50px] xl:h-screen xl:pt-[0px] 2xl:h-screen flex items-center">
          <div className="container mx-auto h-full px-4 flex flex-col lg:flex-row items-center justify-center relative z-10 lg:pt-[50px] lg:py-24">
            <Section4MobileView className="block xl:hidden" features={features} />
            <Section4DesktopView className="hidden xl:flex" features={features} />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Section4;


export const Section4MobileView = ({ className, features = [] }: Section4ViewProps) => {

  const t = useI18n();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [animationStarted, setAnimationStarted] = useState(false);

  const ClickingTriggeredAnimation = () => {
    setAnimationStarted(true);

    setCurrentIndex((prevIndex) => (prevIndex + 1) % features.length);
  };

  return (
    <div className={className}>

      <div onClick={ClickingTriggeredAnimation} className={`w-full pt-12 text-center transform ${currentIndex === 0 ? 'translate-y-[-30px]' : 'translate-y-20'} md:translate-y-0 md:mb-20`}>
        <motion.div
          initial={{ y: 0 }}
          animate={animationStarted ? {
            y: -110,
            transition: {
              duration: 0.7,
              ease: "easeInOut"
            }
          } : {}}
        >
          <h1 className="inline left-1/3 mb-[14px] flex flex-col leading-none mx-auto translate-x-10 translate-y-6">
            <span className="self-center">
              <span className="text-[48px] font-[400] bg-gradient-to-r from-[#2243EA] via-[#2243EA] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text md:text-[64px] uppercase lg:text-[80px]">
                {t('Why')}{' '}
              </span>
              <br className="" />
              <span className="text-[28px] font-[600] lg:text-xl mb-3 text-black md:text-[36px] lg:text-[48px]">{t('Choose CloudSeven')}?</span>
            </span>
          </h1>
        </motion.div>
      </div>

      <div onClick={ClickingTriggeredAnimation} className="w-full h-auto justify-center items-center justify-items-center text-center md:h-full">
        <svg width="0" height="0" viewBox="0 0 651 484" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <clipPath id="cloudClipPathMobile" clipPathUnits="objectBoundingBox" >
              <path
                transform="scale(0.0015, 0.00198)"
                d="M541.197 129.67C525.772 124.059 509.35 120.542 492.262 119.37C486.4 103.376 478.376 88.4284 468.522 74.9045C435.594 29.5599 382.376 0 322.423 0C262.47 0 209.419 29.4761 176.449 74.6952C177.946 74.6952 179.401 74.5696 180.939 74.5696C206.01 74.5696 230.083 78.7147 252.617 86.3768C272.199 71.8899 296.355 63.2648 322.465 63.2648C350.903 63.2648 377.054 73.4391 397.468 90.3963C409.567 100.445 419.628 112.838 426.987 126.906C435.635 143.445 440.541 162.244 440.541 182.174C440.541 184.519 440.458 186.822 440.333 189.125C452.723 184.645 466.069 182.174 479.997 182.174C487.98 182.174 495.796 182.97 503.363 184.519C519.079 187.701 533.672 194.065 546.436 202.858C561.029 212.906 573.253 226.179 582.15 241.629H651C633.122 189.669 592.544 148.302 541.239 129.628L541.197 129.67Z"
              />
              <path
                transform="scale(0.0015, 0.00198)"
                d="M479.956 420.035C451.476 420.035 425.366 409.819 404.952 392.862C400.213 388.926 395.805 384.656 391.773 380.05C385.453 372.89 379.965 364.935 375.516 356.394C366.827 339.855 361.921 321.056 361.921 301.084C361.921 298.74 362.004 296.437 362.129 294.176C362.004 294.176 361.879 294.26 361.796 294.301C361.214 278.768 358.678 263.779 354.479 249.459C349.074 231.162 340.966 214.038 330.489 198.588C321.966 186.027 311.946 174.596 300.638 164.548C289.121 154.29 276.315 145.539 262.471 138.463C249.166 131.68 234.947 126.488 219.98 123.181C207.382 120.375 194.327 118.868 180.94 118.868C171.086 118.868 161.399 119.706 151.961 121.255C134.333 124.144 117.536 129.545 101.986 137.165C41.6594 166.809 0 229.152 0 301.126C0 401.613 81.1568 483.342 180.94 483.342C226.798 483.342 268.665 466.05 300.596 437.663C292.821 427.321 285.92 416.267 279.974 404.628C275.9 396.714 272.283 388.508 269.164 380.05C263.676 386.289 257.523 391.899 250.829 396.882C231.247 411.41 207.091 420.035 180.94 420.035C115.831 420.035 62.8632 366.694 62.8632 301.126C62.8632 267.17 77.1239 236.48 99.866 214.791C111.757 203.486 125.934 194.61 141.692 189C153.998 184.603 167.178 182.217 180.94 182.217C188.964 182.217 196.822 183.054 204.43 184.603C222.225 188.246 238.564 195.908 252.451 206.627C258.479 211.274 264.092 216.466 269.123 222.16C275.775 229.697 281.512 238.113 286.086 247.156C293.985 262.69 298.559 280.191 298.933 298.74C298.933 299.535 298.974 300.331 298.974 301.126C298.974 323.191 302.924 344.377 310.117 363.972C315.272 378.082 322.132 391.397 330.406 403.623C331.529 405.256 332.734 406.847 333.898 408.438C341.632 419.073 350.446 428.912 360.258 437.621C392.188 466.008 434.056 483.3 479.914 483.3C557.412 483.3 623.685 433.978 649.379 364.851H579.489C558.493 397.97 521.74 419.994 479.914 419.994L479.956 420.035Z"
              />
            </clipPath>
          </defs>
        </svg>

        {/****** Start: Full Cloud Strip ******/}
        <motion.div
          style={{ clipPath: 'url(#cloudClipPathMobile)' }}
          className="w-[651px] h-[484px] overflow-hidden scale-50 md:scale-100"
          initial={{ opacity: 1 }}
          animate={animationStarted ? {
            opacity: 0,
            transition: {
              duration: 0.3,
              ease: "easeInOut"
            }
          } : {}}
        >
          <Image
            src={CloudDigital}
            alt="Gallery image"
            className="w-full h-full object-center object-cover"
          />
        </motion.div>
        {/****** End: Full Cloud Strip ******/}

        {animationStarted && (
          <div className="w-screen h-full bg-opacity-1">
            <div className="w-full h-full absolute -top-20 flex flex-col justify-center items-start px-6 md:px-10 z-20">
              <h2 className="text-[24px] font-[600] text-[#33B2ED] leading-[28px] mb-5 text-left md:text-[32px] lg:text-[42px]">{features[currentIndex].title}</h2>
              <p className="text-left font-[400] text-[16px] text-[#002035] md:text-[20px] lg:text-[25px]">{features[currentIndex].description}</p>

              <ul className="mt-4 space-y-2 text-left text-[#002035] text-base md:text-lg leading-relaxed">
                {features[currentIndex].points.map((point, idx) => {
                  const [boldPart, ...rest] = point.split(':');
                  return (
                    <li key={idx} className="flex items-start">
                      <PointFormIcon className="w-5 h-5 mt-[6px] mr-3 flex-shrink-0 md:scale-120 lg:scale-150" />
                      <div className="text-[14px] md:text-[18px] lg:text-[22px]">
                        <strong className="font-[600]">{boldPart}:</strong>{' '}
                        <span className="font-[400]">{rest.join(':')}</span>
                      </div>

                    </li>
                  );
                })}
              </ul>
            </div>

            <motion.div
              key={currentIndex}
              className=""
              initial={{ opacity: 0 }}
              animate={{ opacity: 1, transition: { duration: 0.3, ease: "easeInOut" } }}
            >
              <Image
                src={features[currentIndex].mobileImage}
                alt="Gallery image"
                className="object-center object-cover absolute bottom-0 md:w-[70%]"
              />
            </motion.div>
          </div>
        )}
      </div>
    </div>
  )

};

export const Section4DesktopView = ({ className, features = [] }: Section4ViewProps) => {
  const t = useI18n();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [animationStarted, setAnimationStarted] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setAnimationStarted(true);
    }, 10000);
    return () => clearTimeout(timeout);
  });

  const ClickingTriggeredAnimation = () => {
    setAnimationStarted(true);
    setCurrentIndex((prevIndex) => (prevIndex + 1) % features.length);
  }

  return (
    <div className={className + " -mt-10"}>
      <div className="h-[50%] justify-center items-center justify-items-center text-center w-1/3 px-10 transform translate-x-[50%] translate-y-[4%]">
        {/* First SVG clipPath definition stays as is */}
        <svg width="0" height="0" viewBox="0 0 651 484" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <clipPath id="cloudClipPath" clipPathUnits="objectBoundingBox" >
              <path
                transform="scale(0.0015, 0.00198)"
                d="M541.197 129.67C525.772 124.059 509.35 120.542 492.262 119.37C486.4 103.376 478.376 88.4284 468.522 74.9045C435.594 29.5599 382.376 0 322.423 0C262.47 0 209.419 29.4761 176.449 74.6952C177.946 74.6952 179.401 74.5696 180.939 74.5696C206.01 74.5696 230.083 78.7147 252.617 86.3768C272.199 71.8899 296.355 63.2648 322.465 63.2648C350.903 63.2648 377.054 73.4391 397.468 90.3963C409.567 100.445 419.628 112.838 426.987 126.906C435.635 143.445 440.541 162.244 440.541 182.174C440.541 184.519 440.458 186.822 440.333 189.125C452.723 184.645 466.069 182.174 479.997 182.174C487.98 182.174 495.796 182.97 503.363 184.519C519.079 187.701 533.672 194.065 546.436 202.858C561.029 212.906 573.253 226.179 582.15 241.629H651C633.122 189.669 592.544 148.302 541.239 129.628L541.197 129.67Z"
              />
              <path
                transform="scale(0.0015, 0.00198)"
                d="M479.956 420.035C451.476 420.035 425.366 409.819 404.952 392.862C400.213 388.926 395.805 384.656 391.773 380.05C385.453 372.89 379.965 364.935 375.516 356.394C366.827 339.855 361.921 321.056 361.921 301.084C361.921 298.74 362.004 296.437 362.129 294.176C362.004 294.176 361.879 294.26 361.796 294.301C361.214 278.768 358.678 263.779 354.479 249.459C349.074 231.162 340.966 214.038 330.489 198.588C321.966 186.027 311.946 174.596 300.638 164.548C289.121 154.29 276.315 145.539 262.471 138.463C249.166 131.68 234.947 126.488 219.98 123.181C207.382 120.375 194.327 118.868 180.94 118.868C171.086 118.868 161.399 119.706 151.961 121.255C134.333 124.144 117.536 129.545 101.986 137.165C41.6594 166.809 0 229.152 0 301.126C0 401.613 81.1568 483.342 180.94 483.342C226.798 483.342 268.665 466.05 300.596 437.663C292.821 427.321 285.92 416.267 279.974 404.628C275.9 396.714 272.283 388.508 269.164 380.05C263.676 386.289 257.523 391.899 250.829 396.882C231.247 411.41 207.091 420.035 180.94 420.035C115.831 420.035 62.8632 366.694 62.8632 301.126C62.8632 267.17 77.1239 236.48 99.866 214.791C111.757 203.486 125.934 194.61 141.692 189C153.998 184.603 167.178 182.217 180.94 182.217C188.964 182.217 196.822 183.054 204.43 184.603C222.225 188.246 238.564 195.908 252.451 206.627C258.479 211.274 264.092 216.466 269.123 222.16C275.775 229.697 281.512 238.113 286.086 247.156C293.985 262.69 298.559 280.191 298.933 298.74C298.933 299.535 298.974 300.331 298.974 301.126C298.974 323.191 302.924 344.377 310.117 363.972C315.272 378.082 322.132 391.397 330.406 403.623C331.529 405.256 332.734 406.847 333.898 408.438C341.632 419.073 350.446 428.912 360.258 437.621C392.188 466.008 434.056 483.3 479.914 483.3C557.412 483.3 623.685 433.978 649.379 364.851H579.489C558.493 397.97 521.74 419.994 479.914 419.994L479.956 420.035Z"
              />
            </clipPath>
          </defs>
        </svg>

        {/****** Start: Full Cloud Strip ******/}
        <motion.div
          style={{ clipPath: 'url(#cloudClipPath)' }}
          className="w-[651px] h-[484px] overflow-hidden"
          initial={{ scale: 1 }}
          animate={animationStarted ? { scale: 1, x: -80, y: 60 } : { scale: 1 }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
        >
          {
            animationStarted ?
              (
                <Image
                  src={features[currentIndex].image}
                  alt="Gallery image"
                  className={`w-full h-full object-center object-cover ${animationStarted ? 'scale-100' : 'scale-100'}`}
                />
              )
              :
              (
                <Image
                  src={CloudDigital}
                  alt="Gallery image"
                  className={`w-full h-full object-center object-cover ${animationStarted ? 'scale-100' : 'scale-100'}`}
                />
              )
          }

        </motion.div>
        {/****** End: Full Cloud Strip ******/}
      </div>

      <div className="w-2/3 text-center transform translate-y-1/2">
        <motion.div
          initial={{ scale: 1 }}
          animate={animationStarted ? { scale: 1, x: '30%', y: '-140%' } : { scale: 1 }}
          transition={{ duration: 1.5, ease: "easeInOut" }}
        >
          <h1 className={`${animationStarted ? 'text-left' : ''}`}>
            <span className="self-center">
              <span className={`text-[48px] font-[400] bg-gradient-to-r from-[#2243EA] via-[#2243EA] to-[#33B2ED] font-[900] text-transparent bg-clip-text md:text-[64px] xl:text-[90px] inline-block uppercase`}>
                {t('Why')}{' '}
              </span>
              <span className={`font-[600] mb-3 text-black text-[42px] ${animationStarted ? 'block -mt-7' : 'inline'}`}>{t('Choose CloudSeven')}?</span>
            </span>
          </h1>
        </motion.div>

        {
          animationStarted &&
          (
            <motion.div
              onClick={() => setCurrentIndex((prevIndex) => (prevIndex + 1) % features.length)}
              className="w-[75%] h-full absolute right-0 -top-25 flex flex-col justify-center items-start px-6 md:px-10 z-20"
              initial={{ scale: 1, opacity: 0 }}
              animate={animationStarted ? { scale: 1, opacity: 1 } : { scale: 1, opacity: 0 }}
              transition={{ duration: 1.5, ease: "easeInOut" }}
            >
              <Image src={features[currentIndex].contentBackground} alt="text content background image" className="w-[100%] h-[100%] scale-60 object-center object-cover absolute right-0 transform translate-x-1/3 filter brightness-100 contrast-200 saturate-0 hue-rotate-180" />
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentIndex}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}

                >
                  <h2 className="text-[32px] font-[600] text-[#33B2ED] mb-3 text-left leading-tight">{features[currentIndex].title}</h2>
                  <p className="text-left font-[400] text-[16px] text-[#002035]">{features[currentIndex].description}</p>

                  <ul className="mt-4 space-y-2 text-left text-[#002035] text-base md:text-lg leading-relaxed">
                    {features[currentIndex].points.map((point, idx) => {
                      const [boldPart, ...rest] = point.split(':');
                      return (
                        <li key={idx} className="flex items-start">
                          <PointFormIcon className="w-5 h-5 mt-[6px] mr-3 flex-shrink-0 md:scale-120 lg:scale-150" />
                          <div className="text-[16px]">
                            <strong className="font-[600]">{boldPart}:</strong>{' '}
                            <span className="font-[400]">{rest.join(':')}</span>
                          </div>
                        </li>
                      );
                    })}
                  </ul>
                </motion.div>
              </AnimatePresence>

            </motion.div>
          )
        }
      </div>
    </div>
  );
}