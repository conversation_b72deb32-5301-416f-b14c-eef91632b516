'use client';

import CircleElement2 from '@hi7/assets/background/circle-element2.png';
import FourLineCircuitElement from '@hi7/assets/background/four-line-circuit-element.png';
import ThreeLineCircuitElement from '@hi7/assets/background/three-line-circuit-element.png';
import AnimationFrame from '@hi7/components/AnimationFrame';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import useEmblaCarousel from 'embla-carousel-react';
import { motion } from 'framer-motion';
import { Archivo, Archivo_Black } from 'next/font/google';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

const archivoBlack = Archivo_Black({
  subsets: ['latin'],
  weight: ['400'],
  display: 'swap',
});

const comments = [
  {
    position: 'IT Director, Fortune 500 Technology Company',
    description:
      "CloudSeven's office monitoring system has completely solved our global workforce 'visibility' issue. No matter where our employees are in the world, management can track work progress in real time, significantly improving the efficiency of our global teams.",
    color: 'from-blue-500 to-purple-600',
  },
  {
    position: 'Operations Director, Global Marketing Services Company',
    description:
      'Our team members can access the office environment from anywhere using CloudSeven Cloud Desktop, enabling truly efficient remote work. All files and materials are securely stored in the cloud, and real-time collaboration features ensure seamless teamwork. Our productivity has increased by 200%.',
    color: 'from-pink-500 to-orange-500',
  },
  {
    position: 'Web3 Project Manager Based in Dubai',
    description:
      "Our team members and core community participants are located all over the world. To better manage team collaboration under distributed working conditions and ensure the security of project data, we chose CloudSeven's cloud desktop feature. It has significantly improved our team’s collaboration efficiency in a distributed work environment!",
    color: 'from-green-500 to-teal-500',
  },
  {
    position: 'Marketing Operator, Cross-Border Marketing Service Provide',
    description:
      'We are a company that provides global marketing services for businesses expanding overseas, so we need cloud desktops to create dedicated social media operation environments for each client, ensuring stable performance across platforms. Cloud Seven helps us manage multiple clients, regions, platforms and accounts, while also preventing various account security issues!',
    color: 'from-purple-500 to-pink-500',
  },
];

function Trusted() {
  const t = useI18n();
  const locale = useLocale();

  // Configure carousel to show 2 items per slide
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    align: 'start',
  });

  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);
  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false);
  const [nextBtnEnabled, setNextBtnEnabled] = useState(true);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
    setPrevBtnEnabled(emblaApi.canScrollPrev());
    setNextBtnEnabled(emblaApi.canScrollNext());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;

    setScrollSnaps(emblaApi.scrollSnapList());
    emblaApi.on('select', onSelect);
    onSelect();

    return () => {
      emblaApi.off('select', onSelect);
    };
  }, [emblaApi, onSelect]);

  const scrollTo = useCallback(
    (index: number) => {
      if (!emblaApi) return;
      emblaApi.scrollTo(index);
    },
    [emblaApi],
  );

  // Group comments into pairs for 2 items per slide
  const groupedComments = [];
  for (let i = 0; i < comments.length; i += 2) {
    groupedComments.push(comments.slice(i, i + 2));
  }

  const slideUpVariants = {
    hidden: {
      opacity: 0,
      y: 50,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.1, 0.25, 1],
      },
    },
  };

  return (
    <div
      className="flex min-h-screen items-center justify-center overflow-hidden"
      data-scroll-section
    >
      <div className="w-full">
        <div className="relative h-[110vh] min-h-(--min-sm-h-hvh) overflow-hidden bg-linear-[180deg,#B5E1FF_0.54%,#fff] pb-[50px] md:h-[100vh] md:pb-0 lg:h-full lg:min-h-(--min-h-hvh) lg:pt-[0px] lg:pb-[40px] xl:min-h-[115vh] xl:pt-[50px] 2xl:min-h-screen">
          <div className="relative flex flex-col items-center justify-center px-5 py-11 lg:pt-[60px] xl:pt-[20px]">
            <div className="w-[100%] lg:w-[80dvw] lg:min-w-[855px] xl:w-[90dvw] xl:min-w-[1200px] 2xl:w-[80vw] 2xl:min-w-[1300px]">
              <h1 className="mb-[14px] flex flex-col leading-none xl:mb-[0px] xl:px-10">
                <span
                  className={clsx(
                    archivoBlack.className,
                    'inline-block bg-gradient-to-r from-[#3D5AEC] via-[#33B2ED] to-[#33B2ED] bg-clip-text text-[42px] font-[900] text-transparent uppercase',
                    'lg:inline-block lg:text-[78px]',
                    'xl:text-[64px]',
                    '2xl:text-[80px]',
                  )}
                >
                  {t('Trusted')}
                </span>
              </h1>
              <h3 className="mb-5 text-left text-[28px] leading-none font-[600] text-[#000] lg:text-[48px] xl:px-10">
                {t('by Our Clients')}
              </h3>
              <Image
                src={FourLineCircuitElement}
                alt="Circuit Element"
                className="absolute top-0 right-0 hidden opacity-100 brightness-0 grayscale invert lg:top-1/4 lg:scale-150 xl:top-8 xl:-right-20 xl:block xl:scale-100"
              />
              <div
                ref={emblaRef}
                className="embla mt-10 hidden w-full overflow-hidden xl:block"
              >
                <div className="embla__container flex cursor-grab">
                  {groupedComments.map((commentPair, idx) => (
                    <div
                      key={`slide-${idx}`}
                      className="embla__slide w-full flex-shrink-0 p-5"
                    >
                      <motion.div
                        key={`comment-${idx}`}
                        variants={slideUpVariants}
                        initial="hidden"
                        animate="visible"
                        className="flex flex-col gap-5 px-2 md:flex-row md:gap-10"
                      >
                        {commentPair.map((comment, commentIdx) => (
                          <div
                            key={`comment-${idx}-${commentIdx}`}
                            className={clsx(
                              'w-full md:w-1/2',
                              'relative flex h-full min-h-[330px] flex-col rounded-[15px]',
                              'bg-[linear-gradient(135deg,_rgba(255,255,255,0.6)_0%,_rgba(255,255,255,0.1)_100%)]',
                              'px-5 py-5 shadow-[0px_4px_24px_-1px_rgba(0,0,0,0.2)]',
                              'xl:min-h-[280px]',
                              '2xl:h-[330px]',
                            )}
                          >
                            <div className="flex flex-grow flex-col justify-center gap-4">
                              <div className="text-left text-[16px] leading-[1.4] font-[400] tracking-normal">
                                {t(comment.description)}
                              </div>
                              <b className="text-left text-[14px] leading-[1.2] font-[600] italic">
                                {t(comment.position)}
                              </b>
                            </div>
                          </div>
                        ))}
                      </motion.div>
                    </div>
                  ))}
                </div>
              </div>

              <VerticalSlideStackTestimonial />

              <div className="mt-8 flex hidden w-full justify-center gap-2 xl:flex">
                {scrollSnaps.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => scrollTo(index)}
                    className={`h-2 w-2 rounded-full transition-colors ${index === selectedIndex ? 'bg-[#3D5AEC]' : 'bg-gray-300'}`}
                  />
                ))}
              </div>
              <Image
                src={ThreeLineCircuitElement}
                alt="Circuit Element"
                className="absolute -bottom-30 -left-15 hidden scale-75 rotate-180 transform opacity-80 hue-rotate-170 invert saturate-980 filter xl:block"
              />
              <AnimationFrame variant="FadeIn" once={false} className="">
                <Image
                  src={CircleElement2}
                  alt="Circle Element"
                  className="absolute -top-40 left-[-10%] z-[0] hidden scale-90 opacity-100 xl:flex 2xl:top-[0%] 2xl:left-[-8%] 2xl:scale-130"
                />
              </AnimationFrame>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Trusted;

export const VerticalSlideStackTestimonial: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const t = useI18n();
  const stackVariants = {
    top: { y: 0, scale: 1, zIndex: 3, opacity: 1 },
    middle: { y: '110%', scale: 1, zIndex: 2, opacity: 0.4 },
    bottom: { y: '220%', scale: 1, zIndex: 1, opacity: 0 },
    hidden: { y: '310%', scale: 1, zIndex: 0, opacity: 0 },
  };

  const getStackPosition = (index: number) => {
    const relativeIndex =
      (index - currentIndex + comments.length) % comments.length;
    if (relativeIndex === 0) return 'top';
    if (relativeIndex === 1) return 'middle';
    if (relativeIndex === 2) return 'bottom';
    return 'hidden';
  };

  const nextCard = () => {
    setCurrentIndex((prev) => (prev + 1) % comments.length);
  };

  return (
    <div className="relative mt-15 h-full w-full xl:hidden">
      {comments.map((comment, index) => (
        <motion.div
          key={index}
          variants={stackVariants}
          animate={getStackPosition(index)}
          transition={{ type: 'spring', stiffness: 250, damping: 100 }}
          className={clsx(
            'absolute h-[350px] min-h-[280px] w-full content-center items-center justify-center rounded-xl',
            'cursor-pointer bg-[linear-gradient(135deg,_rgba(255,255,255,0.6)_0%,_rgba(255,255,255,0.1)_100%)] p-6 shadow-[0px_4px_24px_-1px_rgba(0,0,0,0.2)]',
            'md:h-[270px] md:min-h-[230px]',
            'lg:h-[280px] lg:min-h-[250px]',
          )}
          onClick={nextCard}
        >
          <div className="flex flex-grow flex-col justify-center gap-4 self-center">
            <div className="text-left text-[16px] leading-[1.4] font-[400] tracking-normal md:text-[18px] lg:text-[20px]">
              {t(comment.description)}
            </div>
            <b className="text-left text-[14px] leading-[1.2] font-[600] italic md:text-[16px] lg:text-[18px]">
              {t(comment.position)}
            </b>
          </div>
        </motion.div>
      ))}
    </div>
  );
};
