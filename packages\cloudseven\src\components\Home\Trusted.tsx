'use client';

import CircleElement2 from '@hi7/assets/background/circle-element2.png';
import FourLineCircuitElement from '@hi7/assets/background/four-line-circuit-element.png';
import ThreeLineCircuitElement from '@hi7/assets/background/three-line-circuit-element.png';
import AnimationFrame from '@hi7/components/AnimationFrame';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import useEmblaCarousel from 'embla-carousel-react';
import { motion } from 'framer-motion';
import { Archivo, Archivo_Black } from 'next/font/google';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';

const archivo = Archivo({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
});

const archivoBlack = Archivo_Black({
  subsets: ['latin'],
  weight: ['400'],
  display: 'swap',
});

const comments = [
  {
    position: 'IT Director, Fortune 500 Technology Company',
    description: "CloudSeven's office monitoring system has completely solved our global workforce 'visibility' issue. No matter where our employees are in the world, management can track work progress in real time, significantly improving the efficiency of our global teams.",
    color: "from-blue-500 to-purple-600"
  },
  {
    position: 'Operations Director, Global Marketing Services Company',
    description: 'Our team members can access the office environment from anywhere using CloudSeven Cloud Desktop, enabling truly efficient remote work. All files and materials are securely stored in the cloud, and real-time collaboration features ensure seamless teamwork. Our productivity has increased by 200%.',
    color: "from-pink-500 to-orange-500",
  },
  {
    position: 'Web3 Project Manager Based in Dubai',
    description: "Our team members and core community participants are located all over the world. To better manage team collaboration under distributed working conditions and ensure the security of project data, we chose CloudSeven's cloud desktop feature. It has significantly improved our team’s collaboration efficiency in a distributed work environment!",
    color: "from-green-500 to-teal-500",
  },
  {
    position: 'Marketing Operator, Cross-Border Marketing Service Provide',
    description: "We are a company that provides global marketing services for businesses expanding overseas, so we need cloud desktops to create dedicated social media operation environments for each client, ensuring stable performance across platforms. Cloud Seven helps us manage multiple clients, regions, platforms and accounts, while also preventing various account security issues!",
    color: "from-purple-500 to-pink-500"
  },
];

function Trusted() {
  const t = useI18n();
  const locale = useLocale();

  // Configure carousel to show 2 items per slide
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    align: 'start',
  });

  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);
  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false);
  const [nextBtnEnabled, setNextBtnEnabled] = useState(true);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
    setPrevBtnEnabled(emblaApi.canScrollPrev());
    setNextBtnEnabled(emblaApi.canScrollNext());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;

    setScrollSnaps(emblaApi.scrollSnapList());
    emblaApi.on('select', onSelect);
    onSelect();

    return () => {
      emblaApi.off('select', onSelect);
    };
  }, [emblaApi, onSelect]);

  const scrollTo = useCallback((index: number) => {
    if (!emblaApi) return;
    emblaApi.scrollTo(index);
  }, [emblaApi]);

  // Group comments into pairs for 2 items per slide
  const groupedComments = [];
  for (let i = 0; i < comments.length; i += 2) {
    groupedComments.push(comments.slice(i, i + 2));
  }

  const slideUpVariants = {
    hidden: {
      opacity: 0,
      y: 50,
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.1, 0.25, 1],
      },
    },
  };

  return (
    <div className="flex items-center justify-center overflow-hidden">
      <div className="w-full">
        <div className="relative min-h-(--min-sm-h-hvh) h-[110vh] overflow-hidden bg-linear-[180deg,#B5E1FF_0.54%,#fff] pb-[50px] md:pb-0 md:h-[100vh] lg:min-h-(--min-h-hvh) lg:h-full lg:pt-[0px] lg:pb-[40px] xl:pt-[50px] xl:min-h-[115vh] 2xl:min-h-screen">
          <div className="relative flex flex-col items-center justify-center px-5 py-11 lg:pt-[60px] xl:pt-[20px]">
            <div className="w-[100%] lg:w-[80dvw] lg:min-w-[855px] xl:w-[90dvw] xl:min-w-[1200px] 2xl:min-w-[1300px] 2xl:w-[80vw]">
              <h1 className="mb-[14px] flex flex-col leading-none xl:px-10 xl:mb-[0px]">
                <span className={clsx(
                  archivoBlack.className,
                  'bg-gradient-to-r from-[#3D5AEC] via-[#33B2ED] to-[#33B2ED] font-[900] inline-block text-transparent bg-clip-text text-[42px] uppercase',
                  'lg:inline-block lg:text-[78px] ',
                  'xl:text-[64px] ',
                  '2xl:text-[80px]'
                )}>
                  {t('Trusted')}
                </span>
              </h1>
              <h3 className="text-[#000] mb-5 text-left text-[28px] font-[600] leading-none lg:text-[48px] xl:px-10">
                {t('by Our Clients')}
              </h3>
              <Image src={FourLineCircuitElement} alt="Circuit Element" className="hidden invert brightness-0 grayscale xl:block absolute right-0 top-0 opacity-100 lg:scale-150 lg:top-1/4 xl:scale-100 xl:top-8 xl:-right-20" />
              <div ref={emblaRef} className="hidden embla overflow-hidden w-full mt-10 xl:block">
                <div className="embla__container flex cursor-grab">
                  {groupedComments.map((commentPair, idx) => (
                    <div
                      key={`slide-${idx}`}
                      className="embla__slide flex-shrink-0 w-full p-5"
                    >
                      <motion.div
                        key={`comment-${idx}`}
                        variants={slideUpVariants}
                        initial="hidden"
                        animate="visible"
                        className="flex flex-col md:flex-row gap-5 md:gap-10 px-2"
                      >
                        {commentPair.map((comment, commentIdx) => (
                          <div
                            key={`comment-${idx}-${commentIdx}`}
                            className={clsx(
                              'w-full md:w-1/2',
                              'min-h-[330px] h-full relative flex flex-col rounded-[15px]',
                              'bg-[linear-gradient(135deg,_rgba(255,255,255,0.6)_0%,_rgba(255,255,255,0.1)_100%)]',
                              'px-5 shadow-[0px_4px_24px_-1px_rgba(0,0,0,0.2)] py-5',
                              'xl:min-h-[280px]',
                              '2xl:h-[330px]'
                            )}
                          >
                            <div className="flex flex-col gap-4 flex-grow justify-center">
                              <div className="text-[16px] font-[400] leading-[1.4] tracking-normal text-left">
                                {t(comment.description)}
                              </div>
                              <b className="text-[14px] font-[600] leading-[1.2] italic text-left">
                                {t(comment.position)}
                              </b>
                            </div>
                          </div>
                        ))}
                      </motion.div>
                    </div>
                  ))}
                </div>
              </div>

              <VerticalSlideStackTestimonial />

              <div className="hidden flex justify-center mt-8 w-full gap-2 xl:flex">
                {scrollSnaps.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => scrollTo(index)}
                    className={`w-2 h-2 rounded-full transition-colors ${index === selectedIndex ? 'bg-[#3D5AEC]' : 'bg-gray-300'}`}
                  />
                ))}
              </div>
              <Image src={ThreeLineCircuitElement} alt="Circuit Element" className="hidden xl:block absolute -left-15 -bottom-30 transform rotate-180 scale-75 opacity-80 filter invert hue-rotate-170 saturate-980" />
              <AnimationFrame
                variant="FadeIn"
                once={false}
                className=""
              >
                <Image src={CircleElement2} alt="Circle Element" className="hidden xl:flex absolute left-[-10%] -top-40 scale-90 opacity-100 z-[0] 2xl:scale-130 2xl:left-[-8%] 2xl:top-[0%]" />
              </AnimationFrame>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Trusted;


export const VerticalSlideStackTestimonial: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const t = useI18n();
  const stackVariants = {
    top: { y: 0, scale: 1, zIndex: 3, opacity: 1 },
    middle: { y: "110%", scale: 1, zIndex: 2, opacity: 0.4 },
    bottom: { y: "220%", scale: 1, zIndex: 1, opacity: 0 },
    hidden: { y: "310%", scale: 1, zIndex: 0, opacity: 0 }
  };

  const getStackPosition = (index: number) => {
    const relativeIndex = (index - currentIndex + comments.length) % (comments.length);
    if (relativeIndex === 0) return 'top';
    if (relativeIndex === 1) return 'middle';
    if (relativeIndex === 2) return 'bottom';
    return 'hidden';
  };

  const nextCard = () => {
    setCurrentIndex((prev) => (prev + 1) % comments.length);
  };

  return (
    <div className="relative w-full h-full xl:hidden mt-15">
      {comments.map((comment, index) => (
        <motion.div
          key={index}
          variants={stackVariants}
          animate={getStackPosition(index)}
          transition={{ type: "spring", stiffness: 250, damping: 100 }}
          className={clsx(
            "absolute items-center justify-center content-center w-full min-h-[280px] h-[350px] rounded-xl ",
            "bg-[linear-gradient(135deg,_rgba(255,255,255,0.6)_0%,_rgba(255,255,255,0.1)_100%)] shadow-[0px_4px_24px_-1px_rgba(0,0,0,0.2)] p-6 cursor-pointer",
            "md:min-h-[230px] md:h-[270px]",
            "lg:min-h-[250px] lg:h-[280px]",
          )}
          onClick={nextCard}
        >
          <div className="flex flex-col gap-4 flex-grow justify-center self-center">
            <div className="text-[16px] font-[400] leading-[1.4] tracking-normal text-left md:text-[18px] lg:text-[20px]">
              {t(comment.description)}
            </div>
            <b className="text-[14px] font-[600] leading-[1.2] italic text-left md:text-[16px] lg:text-[18px]">
              {t(comment.position)}
            </b>
          </div>
        </motion.div>
      ))}
    </div>
  );
};