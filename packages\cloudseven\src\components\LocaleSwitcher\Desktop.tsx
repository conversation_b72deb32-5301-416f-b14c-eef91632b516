'use client';

import Globe from '@hi7/assets/icon/globe.svg';
import { i18n, type Locale } from '@hi7/lib/i18n';
import { useHeader } from '@hi7/provider/HeaderProvider';
import { useGlobalStore } from '@hi7/provider/ZustandContext';
import clsx from 'clsx';
import { usePathname, useRouter } from 'next/navigation';
import { LANGUAGE_NAME } from './config';

export default function LocaleSwitcher() {
  const pathname = usePathname();
  const router = useRouter();
  const setBaseurl = useGlobalStore((s) => s.setBaseurl);
  const { activeMenu, openMenu } = useHeader();

  // const [currentLocale, currentEndPoint] = pathname.split('/').slice(1) as [
  //   Locale,
  //   string,
  // ];

  // const handleLocaleChange = (locale: Locale) => {
  //   setBaseurl(`/${locale}`);
  //   router.push(`/${locale}${currentEndPoint ? `/${currentEndPoint}` : ''}`);
  // };

  const currentLocale = pathname.split('/')[1] as Locale;

  const handleLocaleChange = (locale: Locale) => {
    const segments = pathname.split('/');
    segments[1] = locale;
    const newPath = segments.join('/');
    setBaseurl(`/${locale}`);
    router.push(newPath);
  };

  return (
    <div
      onClick={() => openMenu('locale')}
      className={clsx('relative flex cursor-pointer items-center gap-2.5')}
    >
      <Globe />

      {activeMenu === 'locale' && (
        <div
          className={clsx(
            'absolute top-full right-0 z-50 mt-7 grid h-fit w-fit rounded-[10px] bg-white',
            'shadow-[0_10px_15px_-3px_rgba(0,0,0,0.1),0_4px_6px_-2px_rgba(0,0,0,0.05),0_-1px_2px_rgba(0,0,0,0.03),-10px_0_20px_-5px_rgba(0,0,0,0.08),10px_0_20px_-5px_rgba(0,0,0,0.08)]',
          )}
          onClick={() => openMenu('locale')}
        >
          {i18n.locales.map((locale) => (
            <button
              key={`locale-${locale}`}
              onClick={() => handleLocaleChange(locale)}
              className={clsx(
                'flex items-center gap-2 whitespace-nowrap',
                'cursor-pointer text-start text-black',
                'px-[14px] py-4',
                'transition-all duration-300',
                'mb-2 w-full min-w-[155px] rounded-[5px] bg-white hover:text-[#FFC525]',
              )}
            >
              {LANGUAGE_NAME[locale] ?? locale}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
