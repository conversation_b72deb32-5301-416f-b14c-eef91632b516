'use client';

import clsx from 'clsx';
import { usePathname, useRouter } from 'next/navigation';

import { i18n, type Locale } from '@hi7/lib/i18n';
import { useGlobalStore } from '@hi7/provider/ZustandContext';
import { LANGUAGE_NAME } from './config';

export default function LocaleSwitcher() {
  const pathname = usePathname();
  const router = useRouter();
  const setBaseurl = useGlobalStore((s) => s.setBaseurl);

  const [, currentEndPoint] = pathname.split('/').slice(1) as [Locale, string];

  const currentLocale = pathname.split('/')[1] as Locale;

  const handleLocaleChange = (locale: Locale) => {
    const segments = pathname.split('/');
    segments[1] = locale;
    const newPath = segments.join('/');
    setBaseurl(`/${locale}`);
    router.push(newPath);
  };

  return i18n.locales.map((locale) => (
    <button
      key={`locale-${locale}`}
      onClick={() => handleLocaleChange(locale)}
      className={clsx('flex flex-col flex-start cursor-pointer items-center gap-2.5 py-4')}
    >
      {LANGUAGE_NAME[locale] ?? locale}
    </button>
  ));
}
