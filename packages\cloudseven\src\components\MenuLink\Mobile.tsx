'use client';

import Link from '@hi7/components/Link';
import SubMenuLink from '@hi7/components/SubMenuLink/Mobile';
import type { MenuLinkProps } from '@hi7/interface/link';
import { useI18n } from '@hi7/lib/i18n';

const MenuLink = (props: MenuLinkProps) => {
  const { url, onClick, children, items = [] } = props;
  const t = useI18n();

  const hasSubitem = items.length > 0;
  if (hasSubitem) {
    return <SubMenuLink {...props} />;
  }

  return (
    <Link
      onClick={onClick}
      url={url}
      className="flex cursor-pointer items-center gap-2.5 py-4"
    >
      {t(children as string)}
    </Link>
  );
};

export default MenuLink;
