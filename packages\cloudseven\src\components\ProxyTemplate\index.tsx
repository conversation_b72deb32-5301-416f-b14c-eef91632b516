'use client';

import ProxyBg from '@hi7/assets/background/proxy-bg.svg';
import ProxyCatBg2 from '@hi7/assets/icon/proxy-1-bg-2.svg';
import proxyCat from '@hi7/assets/lotties/home-client';
import Lot<PERSON> from '@hi7/components/Lottie';
import { useI18n } from '@hi7/lib/i18n';
import { useLocale } from '@hi7/provider/LocaleProvider';
import clsx from 'clsx';
import { useState } from 'react';
import LinkButton from '../Form/LinkButton';
import { KEY_STAT, LOCATIONS, MENUS, PROXY } from './config';

type Template1Props = {
  id:
    | 'proxy-1'
    | 'proxy-2'
    | 'proxy-3'
    | 'proxy-4'
    | 'proxy-5'
    | 'proxy-6'
    | 'proxy-7';
};

function ProxyTemplate({ id }: Template1Props) {
  const {
    benefit,
    useCase,
    cta,
    background,
    lottie,
    title,
    theme = 'dark',
  } = PROXY[id as keyof typeof PROXY];

  const t = useI18n();
  const [selected, setSelected] = useState(MENUS[0].url);
  const locale = useLocale();

  const handleMenuClick =
    (url: string) => (e: React.MouseEvent<HTMLAnchorElement>) => {
      e.preventDefault();
      setSelected(url);
      const el = document.getElementById(url);

      if (el) {
        const y = el.getBoundingClientRect().top + window.scrollY - 150;
        window.scrollTo({ top: y, behavior: 'smooth' });
      }
    };

  return (
    <>
      <div className="flex items-center justify-center overflow-hidden">
        <div className="w-full">
          <div
            className={clsx(
              'relative min-h-(--min-sm-h-hvh) overflow-hidden rounded-br-[40px] rounded-bl-[40px] px-6 pt-[40px] lg:min-h-(--min-h-hvh) lg:rounded-br-[100px] lg:rounded-bl-[100px] lg:px-30',
              theme === 'dark' ? 'text-white' : 'text-[#002035]',
              background.left,
            )}
          >
            <ProxyBg
              className={clsx(
                'absolute right-[-50px] bottom-0 lg:top-0 lg:right-0 lg:h-dvh',
                background.right,
              )}
            />

            <div className="relative flex flex-col items-center justify-center gap-[30px] lg:grid lg:min-h-(--min-h-hvh) lg:grid-cols-[1fr_0.75fr] lg:gap-[15dvw]">
              <div className="flex flex-col items-start lg:gap-[30px]">
                <h3
                  className={clsx(
                    'font-orbitron text-[24px] leading-none font-semibold lg:text-[32px]',
                    theme === 'dark' ? 'text-[#31F4A0]' : 'text-[#007D93]',
                  )}
                >
                  {t(title.h3)}
                </h3>
                <h1 className="mb-4 text-[36px] leading-none lg:mb-0 lg:text-[64px]">
                  {t(title.h1)}
                </h1>
                <p className="mb-4 leading-[1.4] lg:mb-0 lg:text-[18px]">
                  {t(title.p)}
                </p>
                <LinkButton
                  type="main"
                  size="L"
                  className="flex"
                  url={
                    locale === 'en'
                      ? 'https://007tg.com/ccs/elfproxy'
                      : 'https://007tg.com/ccs/elfproxy'
                  }
                  target="_blank"
                >
                  {t(cta)}
                </LinkButton>
              </div>
              <Lottie
                animationData={lottie.data}
                // masking={<lottie.Masking className="w-[55dvw] lg:w-[22dvw]" />}
              />
            </div>
          </div>
        </div>
      </div>
      <div className="mt-2.5 flex items-center justify-center overflow-hidden lg:mt-0">
        <div className="w-full">
          <div className="no-scrollbar mb-[60px] flex gap-8 overflow-x-auto border-b border-[#D9D9D9] px-5 whitespace-nowrap text-[#1E1E1E] lg:items-center lg:justify-center">
            {MENUS.map((item) => (
              <a
                key={`menu-${item.url}`}
                className={clsx(
                  'block cursor-pointer border-b-2 py-3 text-center text-[14px] leading-[1.4] transition-all duration-500',
                  item.url === selected
                    ? 'border-[#007D93] text-[#007D93]'
                    : 'border-white text-[#002035]',
                )}
                onClick={handleMenuClick(item.url)}
              >
                {t(item.text)}
              </a>
            ))}
          </div>

          <div
            className="flex flex-col gap-[100px] px-5 lg:gap-[160px] lg:px-30"
            id={MENUS[0].url}
          >
            <div className="flex flex-col justify-center text-[#002035]">
              <h2 className="mb-[40px] text-[32px] leading-none lg:mb-[60px] lg:text-center lg:text-[48px]">
                {t(MENUS[0].text)}
              </h2>
              <div className="grid shrink-0 gap-[30px] lg:grid-cols-3">
                {benefit.map((item, idx) => (
                  <div
                    key={`proxyt-${idx}`}
                    className="flex flex-col items-center gap-5 py-5 text-[18px]"
                  >
                    <item.Icon className="h-[80px] w-[80px]" />
                    <b className="text-[20px] font-semibold lg:text-[24px]">
                      {t(item.title)}
                    </b>
                    <p>{t(item.desc)}</p>
                  </div>
                ))}
              </div>
            </div>

            <div
              className="flex flex-col justify-center text-[#002035]"
              id={MENUS[1].url}
            >
              <h2 className="mb-[40px] text-[32px] leading-none lg:mb-[60px] lg:text-center lg:text-[48px]">
                {t(MENUS[1].text)}
              </h2>
              <div className="grid shrink-0 gap-[30px] lg:grid-cols-3">
                {useCase.map((item, idx) => (
                  <div
                    key={`proxyt2-${idx}`}
                    className="flex flex-col items-center gap-5 py-5 text-[18px]"
                  >
                    <item.Icon className="h-[80px] w-[80px]" />
                    <b className="text-[20px] font-semibold lg:text-[24px]">
                      {t(item.title)}
                    </b>
                    <p>{t(item.desc)}</p>
                  </div>
                ))}
              </div>
            </div>

            <div
              className="flex flex-col justify-center text-[#002035] lg:m-auto lg:w-[70dvw] lg:items-center"
              id={MENUS[2].url}
            >
              <h2 className="mb-[40px] text-[32px] leading-none lg:mb-[60px] lg:text-[48px]">
                {t(MENUS[2].text)}
              </h2>
              <div className="grid shrink-0 gap-[30px] lg:grid-cols-3">
                {KEY_STAT.map((item, idx) => (
                  <div
                    key={`proxyt3-${idx}`}
                    className="flex flex-col items-center justify-center gap-5 rounded-[20px] border border-[#007D93] p-5 text-center"
                  >
                    <item.Icon className="h-[80px] w-[80px]" />
                    <b className="text-[24px] font-semibold">{t(item.title)}</b>
                    <p className="text-[18px]">{t(item.desc)}</p>
                  </div>
                ))}
              </div>
            </div>

            <div
              className="mb-[85px] flex flex-col justify-center gap-[40px] text-[#002035]"
              id={MENUS[3].url}
            >
              <h2 className="mb-[40px] text-[32px] leading-none lg:mb-[60px] lg:text-center lg:text-[48px]">
                {t(MENUS[3].text)}
              </h2>
              <div className="grid shrink-0 grid-cols-2 gap-[20px] lg:grid-cols-4">
                {LOCATIONS.map((item, idx) => (
                  <div
                    key={`proxy4-${idx}`}
                    className="flex flex-col gap-5 rounded-[8px] border border-[#D9D9D9] px-2.5 py-6 lg:flex-row lg:items-center lg:p-5"
                  >
                    <item.Icon />
                    <div>
                      <b className="text-[18px] font-semibold text-[#000]">
                        {t(item.title)}
                      </b>
                      <p className="text-[14px] text-[#656578]">
                        {t(item.desc)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              <LinkButton
                size="L"
                type="main"
                className="self-start lg:self-center"
                url={
                  locale === 'en'
                    ? 'https://007tg.com/ccs/elfproxy'
                    : 'https://007tg.com/ccs/elfproxy'
                }
                target="_blank"
              >
                {t('Start Free Trial')}
              </LinkButton>
            </div>
          </div>

          <div className="relative h-[120px] w-screen overflow-hidden lg:h-[150px]">
            <div className="animate-infinite-background flex w-[calc(1440px*4)]">
              <ProxyCatBg2 height="100%" />
              <ProxyCatBg2 height="100%" />
              <ProxyCatBg2 height="100%" />
              <ProxyCatBg2 height="100%" />
            </div>

            <div className="absolute bottom-[-25px] left-[50%] h-[100px] w-[100px] translate-x-[-50%] lg:bottom-[-40px] lg:h-[150px] lg:w-[150px]">
              <Lottie animationData={proxyCat} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default ProxyTemplate;
