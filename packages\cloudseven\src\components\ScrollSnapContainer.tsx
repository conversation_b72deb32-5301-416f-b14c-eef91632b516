'use client';

import React, { useEffect, useRef, ReactNode } from 'react';

interface ScrollSnapContainerProps {
  children: ReactNode;
}

const ScrollSnapContainer: React.FC<ScrollSnapContainerProps> = ({ children }) => {
  const isScrolling = useRef(false);
  const currentSectionIndex = useRef(0);
  const targetSectionIndex = useRef(-1);
  const sectionsRef = useRef<HTMLElement[]>([]);

  useEffect(() => {
    // Get all sections with data-scroll-section attribute
    sectionsRef.current = Array.from(document.querySelectorAll('[data-scroll-section]'));

    const handleWheel = (e: WheelEvent) => {
      // Check if scroll is locked by other components (like Landing section)
      if (document.body.dataset.scrollLock === 'true') {
        return;
      }
      
      if (isScrolling.current) {
        return;
      }

      const scrollDirection = e.deltaY > 0 ? 'down' : 'up';
      let nextIndex = currentSectionIndex.current;

      if (scrollDirection === 'down') {
        nextIndex = Math.min(sectionsRef.current.length - 1, currentSectionIndex.current + 1);
      } else {
        nextIndex = Math.max(0, currentSectionIndex.current - 1);
      }

      if (nextIndex !== currentSectionIndex.current) {
        // Use timeout to allow other event handlers to run first
        setTimeout(() => {
          // Re-check the lock inside the timeout
          if (document.body.dataset.scrollLock === 'true' || isScrolling.current) {
            return;
          }
          
          e.preventDefault();
          isScrolling.current = true;
          targetSectionIndex.current = nextIndex;
          sectionsRef.current[nextIndex].scrollIntoView({ behavior: 'smooth' });
        }, 0);
      }
    };

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const index = sectionsRef.current.findIndex(sec => sec === entry.target);
            if (index !== -1) {
              currentSectionIndex.current = index;
              // If we have arrived at our target destination, release the scroll lock
              if (index === targetSectionIndex.current) {
                isScrolling.current = false;
                targetSectionIndex.current = -1;
              }
            }
          }
        });
      },
      { threshold: 0.8 } // Section is considered "current" when 80% is visible
    );

    // Observe all sections
    sectionsRef.current.forEach(section => observer.observe(section));
    window.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      window.removeEventListener('wheel', handleWheel);
      sectionsRef.current.forEach(section => observer.unobserve(section));
    };
  }, []);

  return (
    <div>
      {children}
    </div>
  );
};

export default ScrollSnapContainer;
