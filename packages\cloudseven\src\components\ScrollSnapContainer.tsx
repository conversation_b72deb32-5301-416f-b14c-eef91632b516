'use client';

import type React from 'react';
import type { ReactNode } from 'react';
import { useCallback, useEffect, useRef } from 'react';

interface ScrollSnapContainerProps {
  children: ReactNode;
}

const ScrollSnapContainer: React.FC<ScrollSnapContainerProps> = ({
  children,
}) => {
  const isScrolling = useRef(false);
  const currentSectionIndex = useRef(0);
  const sectionsRef = useRef<HTMLElement[]>([]);

  // Function to get the currently most visible section
  const getCurrentSection = () => {
    if (sectionsRef.current.length === 0) return 0;

    const viewportHeight = window.innerHeight;
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const viewportCenter = scrollTop + viewportHeight / 2;

    let closestIndex = 0;
    let closestDistance = Infinity;

    sectionsRef.current.forEach((section, index) => {
      const rect = section.getBoundingClientRect();
      const sectionTop = rect.top + scrollTop;
      const sectionCenter = sectionTop + rect.height / 2;
      const distance = Math.abs(viewportCenter - sectionCenter);

      if (distance < closestDistance) {
        closestDistance = distance;
        closestIndex = index;
      }
    });

    return closestIndex;
  };

  // Function to update current section
  const updateCurrentSection = useCallback(() => {
    const newIndex = getCurrentSection();
    if (newIndex !== currentSectionIndex.current) {
      currentSectionIndex.current = newIndex;
      console.log(`ScrollSnap: Current section updated to ${newIndex}`);
    }
  }, []);

  useEffect(() => {
    // Add global styles for smooth scrolling
    document.documentElement.style.scrollBehavior = 'smooth';

    // Initialize sections and observer
    const initializeScrollSnap = () => {
      // Get all sections with data-scroll-section attribute
      sectionsRef.current = Array.from(
        document.querySelectorAll('[data-scroll-section]'),
      );
      console.log('ScrollSnap: Found sections:', sectionsRef.current.length);

      // Log section details for debugging
      sectionsRef.current.forEach((section, index) => {
        const sectionName =
          section.querySelector('h1, h2, h3')?.textContent?.slice(0, 30) ||
          section.tagName.toLowerCase() ||
          `Section ${index + 1}`;
        console.log(
          `ScrollSnap: Section ${index}: ${sectionName}`,
          section.tagName,
          section.className.slice(0, 50),
        );
      });

      if (sectionsRef.current.length === 0) {
        console.warn(
          'ScrollSnap: No sections found with data-scroll-section attribute',
        );
        return;
      }

      // Set initial current section
      updateCurrentSection();
    };

    // Initialize after a short delay to ensure DOM is ready
    const timer = setTimeout(initializeScrollSnap, 100);

    const handleWheel = (e: WheelEvent) => {
      // Re-query sections if needed
      if (sectionsRef.current.length === 0) {
        sectionsRef.current = Array.from(
          document.querySelectorAll('[data-scroll-section]'),
        );
      }

      if (sectionsRef.current.length === 0) {
        console.log('ScrollSnap: No sections available');
        return;
      }

      // Update current section before calculating next
      updateCurrentSection();

      // Check if scroll is locked by other components (like Landing section)
      if (document.body.dataset.scrollLock === 'true') {
        console.log('ScrollSnap: Scroll locked by other component');
        return;
      }

      if (isScrolling.current) {
        console.log('ScrollSnap: Already scrolling, ignoring wheel event');
        e.preventDefault();
        e.stopPropagation();
        return;
      }

      // Check if we're in Section3 and it's handling internal navigation
      const currentSection = sectionsRef.current[currentSectionIndex.current];
      if (currentSection) {
        const section3Element = currentSection.querySelector(
          '[data-scroll-section] > div > div[class*="challenges"]',
        );
        if (section3Element) {
          const rect = currentSection.getBoundingClientRect();
          const isSection3InView =
            rect.top <= window.innerHeight && rect.bottom >= 0;

          if (isSection3InView) {
            // Let Section3 handle its internal navigation
            console.log(
              'ScrollSnap: Letting Section3 handle internal navigation',
            );
            return;
          }
        }
      }

      const scrollDirection = e.deltaY > 0 ? 'down' : 'up';
      let nextIndex = currentSectionIndex.current;

      if (scrollDirection === 'down') {
        nextIndex = Math.min(
          sectionsRef.current.length - 1,
          currentSectionIndex.current + 1,
        );
      } else {
        nextIndex = Math.max(0, currentSectionIndex.current - 1);
      }

      console.log(
        `ScrollSnap: Current: ${currentSectionIndex.current}, Next: ${nextIndex}, Direction: ${scrollDirection}`,
      );

      if (nextIndex !== currentSectionIndex.current) {
        e.preventDefault();
        e.stopPropagation();
        isScrolling.current = true;

        console.log(`ScrollSnap: Scrolling to section ${nextIndex}`);
        sectionsRef.current[nextIndex].scrollIntoView({ behavior: 'smooth' });

        // Update current section immediately
        currentSectionIndex.current = nextIndex;

        // Reset scrolling flag after animation
        setTimeout(() => {
          isScrolling.current = false;
          updateCurrentSection(); // Double-check current section after animation
          console.log('ScrollSnap: Scroll animation completed');
        }, 600);
      }
    };

    // Add scroll listener to track current section during normal scrolling
    const handleScroll = () => {
      if (!isScrolling.current) {
        updateCurrentSection();
      }
    };

    // Add keyboard navigation for testing
    const handleKeyDown = (e: KeyboardEvent) => {
      if (document.body.dataset.scrollLock === 'true' || isScrolling.current) {
        return;
      }

      if (e.key === 'ArrowDown' || e.key === 'PageDown') {
        e.preventDefault();
        const nextIndex = Math.min(
          sectionsRef.current.length - 1,
          currentSectionIndex.current + 1,
        );
        if (nextIndex !== currentSectionIndex.current) {
          isScrolling.current = true;
          sectionsRef.current[nextIndex].scrollIntoView({ behavior: 'smooth' });
          currentSectionIndex.current = nextIndex;
          setTimeout(() => {
            isScrolling.current = false;
            updateCurrentSection();
          }, 600);
        }
      } else if (e.key === 'ArrowUp' || e.key === 'PageUp') {
        e.preventDefault();
        const nextIndex = Math.max(0, currentSectionIndex.current - 1);
        if (nextIndex !== currentSectionIndex.current) {
          isScrolling.current = true;
          sectionsRef.current[nextIndex].scrollIntoView({ behavior: 'smooth' });
          currentSectionIndex.current = nextIndex;
          setTimeout(() => {
            isScrolling.current = false;
            updateCurrentSection();
          }, 600);
        }
      }
    };

    // Add event listeners with capture to run before other handlers
    window.addEventListener('wheel', handleWheel, {
      passive: false,
      capture: true,
    });
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('keydown', handleKeyDown, { passive: false });

    return () => {
      clearTimeout(timer);
      window.removeEventListener('wheel', handleWheel, { capture: true });
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('keydown', handleKeyDown);
      // Reset scroll behavior
      document.documentElement.style.scrollBehavior = '';
    };
  }, [updateCurrentSection]);

  return <div>{children}</div>;
};

export default ScrollSnapContainer;
