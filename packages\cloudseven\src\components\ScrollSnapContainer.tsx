'use client';

import React, { ReactNode, useEffect, useRef } from 'react';

interface ScrollSnapContainerProps {
  children: ReactNode;
}

const ScrollSnapContainer: React.FC<ScrollSnapContainerProps> = ({
  children,
}) => {
  const isScrolling = useRef(false);
  const currentSectionIndex = useRef(0);
  const targetSectionIndex = useRef(-1);
  const sectionsRef = useRef<HTMLElement[]>([]);

  useEffect(() => {
    // Add global styles for smooth scrolling
    document.documentElement.style.scrollBehavior = 'smooth';

    // Delay to ensure DOM is fully rendered
    const initializeScrollSnap = () => {
      // Get all sections with data-scroll-section attribute
      sectionsRef.current = Array.from(
        document.querySelectorAll('[data-scroll-section]'),
      );
      console.log('ScrollSnap: Found sections:', sectionsRef.current.length);

      // Log section details for debugging
      sectionsRef.current.forEach((section, index) => {
        const sectionName =
          section.querySelector('h1, h2, h3')?.textContent?.slice(0, 30) ||
          `Section ${index + 1}`;
        console.log(
          `ScrollSnap: Section ${index}: ${sectionName}`,
          section.tagName,
        );
      });

      if (sectionsRef.current.length === 0) {
        console.warn(
          'ScrollSnap: No sections found with data-scroll-section attribute',
        );
        return;
      }
    };

    // Initialize after a short delay to ensure DOM is ready
    const timer = setTimeout(initializeScrollSnap, 100);

    const handleWheel = (e: WheelEvent) => {
      // Re-query sections if needed
      if (sectionsRef.current.length === 0) {
        sectionsRef.current = Array.from(
          document.querySelectorAll('[data-scroll-section]'),
        );
      }

      // Check if scroll is locked by other components (like Landing section)
      if (document.body.dataset.scrollLock === 'true') {
        console.log('ScrollSnap: Scroll locked by other component');
        return;
      }

      if (isScrolling.current) {
        console.log('ScrollSnap: Already scrolling, ignoring wheel event');
        e.preventDefault();
        return;
      }

      if (sectionsRef.current.length === 0) {
        console.log('ScrollSnap: No sections available');
        return;
      }

      const scrollDirection = e.deltaY > 0 ? 'down' : 'up';
      let nextIndex = currentSectionIndex.current;

      if (scrollDirection === 'down') {
        nextIndex = Math.min(
          sectionsRef.current.length - 1,
          currentSectionIndex.current + 1,
        );
      } else {
        nextIndex = Math.max(0, currentSectionIndex.current - 1);
      }

      console.log(
        `ScrollSnap: Current: ${currentSectionIndex.current}, Next: ${nextIndex}, Direction: ${scrollDirection}`,
      );

      if (nextIndex !== currentSectionIndex.current) {
        e.preventDefault();
        isScrolling.current = true;
        targetSectionIndex.current = nextIndex;

        console.log(`ScrollSnap: Scrolling to section ${nextIndex}`);
        sectionsRef.current[nextIndex].scrollIntoView({ behavior: 'smooth' });

        // Reset scrolling flag after animation
        setTimeout(() => {
          isScrolling.current = false;
          targetSectionIndex.current = -1;
          console.log('ScrollSnap: Scroll animation completed');
        }, 1000);
      }
    };

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = sectionsRef.current.findIndex(
              (sec) => sec === entry.target,
            );
            if (index !== -1) {
              currentSectionIndex.current = index;
              console.log(`ScrollSnap: Current section updated to ${index}`);
            }
          }
        });
      },
      { threshold: 0.5 }, // Section is considered "current" when 50% is visible
    );

    // Set up observer after sections are found
    const setupObserver = () => {
      if (sectionsRef.current.length > 0) {
        sectionsRef.current.forEach((section) => observer.observe(section));
        console.log(
          'ScrollSnap: Observer set up for',
          sectionsRef.current.length,
          'sections',
        );
      }
    };

    // Setup observer after initialization
    setTimeout(setupObserver, 200);

    window.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      clearTimeout(timer);
      window.removeEventListener('wheel', handleWheel);
      sectionsRef.current.forEach((section) => observer.unobserve(section));
      // Reset scroll behavior
      document.documentElement.style.scrollBehavior = '';
    };
  }, []);

  return <div>{children}</div>;
};

export default ScrollSnapContainer;
