'use client';

import React from 'react';
import ScrollSnapContainer from './ScrollSnapContainer';

const ScrollSnapTest: React.FC = () => {
  return (
    <ScrollSnapContainer>
      <div 
        data-scroll-section 
        className="h-screen bg-red-500 flex items-center justify-center text-white text-4xl font-bold"
      >
        Section 1 - Red
      </div>
      <div 
        data-scroll-section 
        className="h-screen bg-blue-500 flex items-center justify-center text-white text-4xl font-bold"
      >
        Section 2 - Blue
      </div>
      <div 
        data-scroll-section 
        className="h-screen bg-green-500 flex items-center justify-center text-white text-4xl font-bold"
      >
        Section 3 - Green
      </div>
      <div 
        data-scroll-section 
        className="h-screen bg-yellow-500 flex items-center justify-center text-black text-4xl font-bold"
      >
        Section 4 - Yellow
      </div>
    </ScrollSnapContainer>
  );
};

export default ScrollSnapTest;
