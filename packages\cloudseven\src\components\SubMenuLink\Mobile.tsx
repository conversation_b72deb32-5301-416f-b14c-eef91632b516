'use client';

import ChevronDown from '@hi7/assets/icon/chevron-down.svg';
import ChevronUp from '@hi7/assets/icon/chevron-up.svg';
import type { MenuLinkProps } from '@hi7/interface/link';
import { useI18n } from '@hi7/lib/i18n';
import clsx from 'clsx';
import { useState } from 'react';
import Link from '../Link';

const SubMenuLinkMobile = ({
  children,
  onClick,

  items = [],
}: OmitStrict<MenuLinkProps, 'asButton'>) => {
  const hasSubitem = items.length > 0;

  const [isOpen, setIsOpen] = useState(false);
  const t = useI18n();

  return (
    <>
      <div
        onClick={() => {
          setIsOpen((prev) => !prev);
        }}
        className={'flex cursor-pointer items-center gap-2.5 py-4 text-[24px] font-[400]'}
      >
        {t(children as string)}
        {isOpen ? <ChevronUp /> : <ChevronDown />}
      </div>

      {hasSubitem && (
        <>
          {isOpen && (
            <div className="flex flex-col gap-y-4 mb-5">
              {items.map(({ url, text, order }) => (
                <Link
                  onClick={(e) => onClick?.(e)}
                  url={url}
                  key={`${text}-${url}`}
                  className={clsx(
                    'grid h-full grid-cols-[auto_1fr] items-center gap-x-2.5',
                    'cursor-pointer text-start text-black ml-3',
                    order,
                  )}
                >
                  <div className="text-sm">
                    <b className="font-[400] text-[24px]">{t(text)}</b>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </>
      )}
    </>
  );
};

export default SubMenuLinkMobile;
