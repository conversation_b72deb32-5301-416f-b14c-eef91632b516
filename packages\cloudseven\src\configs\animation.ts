import type { Target, Transition } from 'motion/react';

export type VariantType =
  | 'SlideIn'
  | 'SlideOut'
  | 'SlideUp'
  | 'SlideUpSlightly'
  | 'FadeIn'
  | 'SlideDown'
  | 'SlideDownHomeHero'
  | 'SlideRightDownHomeHero'
  | 'SlideDownSlightly'
  | 'SlideDownDelay'
  | 'SlideUp45AtEase'
  | 'FadeInHomeSection4Card'
  | 'SlideUpHomeSection4'
  | 'HomeHeroMoveRightRotate';

type MotionConfig = {
  initial: Target;
  animate: Target;
  transition: Transition;
};

export const defaultAnimateConfig: Record<VariantType, MotionConfig> = {
  SlideIn: {
    initial: { x: 100, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },
  SlideOut: {
    initial: { x: -100, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },
  SlideUp: {
    initial: { x: '5%', y: 200, opacity: 0 },
    animate: { x: '5%', y: -100, opacity: 1 },
    transition: { duration: 0.9, ease: 'easeIn' },
  },
  SlideDown: {
    initial: { y: -450, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 1.2, delay: 0.1 },
  },
  SlideDownHomeHero: {
    initial: { y: 0, opacity: 1 },
    animate: { y: 100, opacity: 1 },
    transition: { duration: 1, delay: 1.5 },
  },
  SlideRightDownHomeHero: {
    initial: { x: 0, y: -245, opacity: 0 },
    animate: { x: 100, y: -30, opacity: 1 },
    transition: { duration: 1, delay: 1.5 },
  },
  SlideDownDelay: {
    initial: { y: -250, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 1.2, delay: 0.5 },
  },
  FadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.8, delay: 1.5 },
  },

  SlideUp45AtEase: {
    initial: { x: '-100%', y: '400%', opacity: 0 },
    animate: { x: 0, y: 0, opacity: 1 },
    transition: { duration: 1, ease: 'easeOut' },
  },

  SlideDownSlightly: {
    initial: { y: -280, opacity: 1 },
    animate: { y: -35, opacity: 1 },
    transition: { duration: 1.0, delay: 1.8, ease: 'easeIn' },
  },

  SlideUpSlightly: {
    initial: { y: 0, opacity: 1, },
    animate: { y: -350, opacity: 1 },
    transition: { duration: 1.0, delay: 1.8, ease: 'easeIn' },
  },

  SlideUpHomeSection4: {
    initial: { y: 300, opacity: 1 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 1.0, delay: 1.1, ease: 'easeIn' },
  },

  FadeInHomeSection4Card: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 2, delay: 1.8, ease: 'easeIn' },
  },
  HomeHeroMoveRightRotate: {
    initial: { x: 0, y: 0, opacity: 1, rotate: 0 },
    animate: { x: '75%', y: '-15%', opacity: 1, rotate: -90 },
    transition: { duration: 1.3, ease: 'easeOut', delay: 1.5 },
  }



};
