import type { FilterProductTagId } from '@hi7/interface/info-card';

export const TAGS_LABEL = [
  'ctrlFire',
  'elfChampion',
  'cloudseven',
  'promoPicasso',
  'imx',
  'elfProxy',
  'echoData',
  'workGram',
  'general',
] as const;

export const PRODUCT_TAG_IDS: Record<FilterProductTagId, number> = {
  cloudseven: 202,
  ctrlfire: 200,
  echodata: 203,
  elfproxy: 204,
  imx: 205,
  picasso: 206,
  elf: 207,
  workgram: 208,
  general: 209,
};

export const TAG_MAP: {
  [key: number]: { name: string; value: (typeof TAGS_LABEL)[number] };
} = {
  [PRODUCT_TAG_IDS.ctrlfire]: { name: 'CtrlFire', value: 'ctrlFire' },
  [PRODUCT_TAG_IDS.imx]: { name: 'I<PERSON>', value: 'imx' },
  [PRODUCT_TAG_IDS.picasso]: { name: 'Promo Picasso', value: 'promoPicasso' },
  [PRODUCT_TAG_IDS.workgram]: { name: 'WorkGram', value: 'workGram' },
  [PRODUCT_TAG_IDS.echodata]: { name: 'EchoData', value: 'echoData' },
  [PRODUCT_TAG_IDS.cloudseven]: { name: 'cloudseven', value: 'cloudseven' },
  [PRODUCT_TAG_IDS.elf]: { name: 'elf Champion', value: 'elfChampion' },
  [PRODUCT_TAG_IDS.elfproxy]: { name: 'ElfProxy', value: 'elfProxy' },
};

export const TAG_MAP_NAME: { [key: number]: string } = Object.fromEntries(
  Object.entries(TAG_MAP).map(([key, { name }]) => [key, name]),
);

export const TAG_MAP_VALUE: { [key: number]: (typeof TAGS_LABEL)[number] } =
  Object.fromEntries(
    Object.entries(TAG_MAP).map(([key, { value }]) => [key, value]),
  );
