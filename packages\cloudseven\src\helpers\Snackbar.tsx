import Snackbar from '@hi7/components/Snackbar';
import type { SnackbarType } from '@hi7/interface/snackbar';
import type React from 'react';
import { useEffect } from 'react';
import type { Root } from 'react-dom/client';
import { createRoot } from 'react-dom/client';

const AUTO_HIDE = 3000;

class SnackbarManager {
  private static instance: SnackbarManager;
  private showSnackbar: (message: string, type: SnackbarType) => void;
  private snackbarRoot: HTMLDivElement | null = null;
  private root: Root | null = null;

  private constructor() {
    this.showSnackbar = this.createShowSnackbar();
    this.initializeSnackbarRoot();
  }

  public static getInstance(): SnackbarManager {
    if (!SnackbarManager.instance) {
      SnackbarManager.instance = new SnackbarManager();
    }
    return SnackbarManager.instance;
  }

  private initializeSnackbarRoot() {
    const existingRoot = document.getElementById('snackbar-root');
    if (!existingRoot) {
      const root = document.createElement('div');
      root.setAttribute('id', 'snackbar-root');
      document.body.appendChild(root);
      this.snackbarRoot = root;
    } else {
      this.snackbarRoot = existingRoot as HTMLDivElement;
    }

    if (this.snackbarRoot) {
      this.root = createRoot(this.snackbarRoot);
    }
  }

  private createShowSnackbar() {
    return (message: string, type: SnackbarType) => {
      if (this.root) {
        this.root.unmount();
        this.initializeSnackbarRoot();
        this.root.render(
          <SnackbarContainer
            message={message}
            onClose={() => {
              this.removeSnackbar();
            }}
            type={type}
          />,
        );
      } else {
        console.error('Snackbar root is not initialized.');
      }
    };
  }

  private removeSnackbar() {
    if (this.root) {
      this.root.unmount();
    }
  }

  public notify(message: string, type: SnackbarType) {
    this.showSnackbar(message, type);
  }
}

interface SnackbarContainerProps {
  message: string;
  onClose: () => void;
  type: SnackbarType;
}

const SnackbarContainer: React.FC<SnackbarContainerProps> = ({
  message,
  onClose,
  type,
}) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, AUTO_HIDE);
    return () => clearTimeout(timer);
  }, [onClose]);

  return <Snackbar message={message} show type={type} />;
};

export const notify = (message: string, type: SnackbarType = 'success') => {
  SnackbarManager.getInstance().notify(message, type);
};
