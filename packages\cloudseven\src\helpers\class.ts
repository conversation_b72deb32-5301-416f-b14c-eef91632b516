import type { InputType } from '@hi7/interface/input';

type LabelClassProps = {
  variant: InputType;
  value: unknown;
};

const INPUT_STYLE: Record<InputType, (value: unknown) => string> = {
  Outlined: () =>
    'border rounded-lg bg-hi7-white focus-visible:border-hi7-primary outline-hidden border-hi7-border text-[1rem] p-2.5',
  Standard: () =>
    `outline-hidden border-b border-hi7-input-border hover:border-hi7-primary text-[1.125rem] bg-transparent py-2.5`,
};

export const getInputClasses = ({ variant, value }: LabelClassProps) => {
  return `block w-full text-hi7-font resize-none 
    placeholder-hi7-placeholder focus:ring-hi7-primary focus:border-hi7-primary
    ${INPUT_STYLE[variant](value)}
    `;
};
