'use server';

import { FEATURE_FLAG_IDS } from '@hi7/configs/feature-flag';
import { getVisitorId } from '@hi7/configs/visitor';
import type { FeatureFlag, FeatureFlagId } from '@hi7/interface/feature-flag';

export const isFullPageScrollEnabled = async () => {
  getFeatureFlag('feat-80247500');
  return;
};
export const isRemoveProductsEnabled = async () => {
  getFeatureFlag('feat-80264622');
  return;
};
export const getFeatureFlag = async (
  flagId: FeatureFlagId,
): Promise<boolean> => {
  try {
    const visitorId = await getVisitorId();
    const response = await fetch(`${process.env.FF_API_URL}/flag/check`, {
      method: 'POST',
      body: JSON.stringify({
        environment_key:
          process.env.ENVIRONMENT === 'production' ? 'production' : 'staging',
        key: `baymax-${flagId}`,
        context: {
          key: visitorId,
        },
        default_value: false,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const { result } = await response.json();
    return result as boolean;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (ex) {
    console.log(ex, process.env.FF_API_URL);
    return false;
  }
};

export const getFeatureFlags = async (): Promise<FeatureFlag> => {
  return FEATURE_FLAG_IDS.reduce(
    async (acc, flagId) => {
      const flags = (await acc) as FeatureFlag;
      flags[flagId] = await getFeatureFlag(flagId);
      return flags;
    },
    Promise.resolve({} as FeatureFlag),
  );
};
