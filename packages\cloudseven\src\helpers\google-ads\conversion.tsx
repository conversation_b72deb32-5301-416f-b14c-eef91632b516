'use client';

type ConversionOptions = {
  conversionLabel: string;
  tagId?: string;
};

/**
 *
 * @param tagId e.g. AW-16734984049
 * @param conversionLabel e.g. RJXcCK-A5dsZEPGu7qs-
 */
export const useGaConversion = ({
  conversionLabel,
  tagId = process.env.NEXT_PUBLIC_HI7_GA_TAG,
}: ConversionOptions) => {
  const invokeConversion = () => {
    window.gtag('event', 'conversion', {
      send_to: `${tagId}/${conversionLabel}`,
    });
  };

  return invokeConversion;
};
