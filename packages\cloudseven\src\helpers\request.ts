import { LOCALE } from '@hi7/configs/dictionary';
import { type requestMethod } from '@hi7/interface/request';
import type { Locale } from '@hi7/lib/i18n';
import { generateRequestContent } from './string';
type Data = {
  message?: string | object;
};

type RequestProps<U = unknown> = {
  method: requestMethod;
  url: string;
  body?: U;
  localApi?: boolean;
  locale?: Locale;
};

export async function post<T, U>(url: string, body: U, locale: Locale) {
  return request<T, U>({ method: 'POST', url, body, locale });
}

export async function postLocalApi<T, U>(url: string, body: U) {
  return request<T, U>({ method: 'POST', url, body, localApi: true });
}

export async function get<T>(url: string) {
  return request<T, undefined>({ method: 'POST', url });
}

export async function getLocalApi<T>(url: string) {
  return request<T, undefined>({ method: 'GET', url, localApi: true });
}

export async function patchLocalApi<T, U>(url: string, body: U) {
  return request<T, U>({ method: 'PATCH', url, body, localApi: true });
}

export async function deleteLocalApi<T, U>(url: string, body: U) {
  return request<T, U>({ method: 'DELETE', url, body, localApi: true });
}

async function transform<T>(response: Response) {
  if (response.status === 204) {
    return;
  }

  const data = await response.json();

  if (!response.ok) {
    throw new Error(getErrorMessage(data));
  }

  return data as T;
}

function buildHeaders(locale?: string, isForm?: boolean) {
  return {
    ...(!isForm ? { 'Content-Type': 'application/json' } : {}),
    ...(locale ? { 'Accept-Language': locale } : {}),
  };
}

async function request<T, U>({
  method,
  url,
  body,
  localApi = false,
  locale = 'en',
}: RequestProps<U>) {
  const apiLocale = LOCALE[locale];
  try {
    const response = await fetch(
      `${!localApi ? process.env.NEXT_PUBLIC_API_URL : ''}${url}`,
      {
        method,
        headers: buildHeaders(apiLocale, body instanceof FormData),
        body: generateRequestContent<U>(body, method),
      },
    );

    return transform<T>(response);
  } catch (error) {
    throw new Error((error as Error).message);
  }
}

const getErrorMessage = (data: Data) => {
  if (!data?.message) {
    return '';
  }

  return typeof data.message === 'object'
    ? JSON.stringify(data.message)
    : data.message;
};
