import type { requestMethod } from '@hi7/interface/request';
export const interpolate = (
  text: string,
  placeholders: Record<string, string | number>,
): string => {
  return text.replace(/{{(.*?)}}/g, (_match, key) => `${placeholders[key]}`);
};

export const generateRequestContent = <U>(
  body: U | undefined,
  method: requestMethod,
) => {
  const bodyContent =
    body instanceof FormData || body instanceof File
      ? body
      : method !== 'GET'
        ? JSON.stringify(body)
        : undefined;

  return bodyContent;
};

export function convertObjectToURLParams<T extends Record<string, unknown>>(
  data: T,
): URLSearchParams {
  const params = new URLSearchParams();

  for (const [key, values] of Object.entries(data)) {
    if (values) {
      if (Array.isArray(values)) {
        const filteredValues = values.filter((v) => v != null);
        filteredValues.forEach((v) => params.append(key, `${v}`));
      } else {
        params.append(key, `${values}`);
      }
    }
  }

  return params;
}
