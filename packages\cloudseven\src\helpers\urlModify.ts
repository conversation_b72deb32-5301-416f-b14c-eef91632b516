export const modifyUrl = (url: string): string | null => {
  try {
    const webUrl = new URL(url);
    webUrl.search = '';

    if (!webUrl.hostname.startsWith('www.')) {
      webUrl.hostname = `${webUrl.hostname}`;
    }

    if (webUrl.hostname.startsWith('www.')) {
      webUrl.hostname = `${webUrl.hostname.replace('www.', '')}`;
    }

    return removeLastSlash(`${webUrl}`);
  } catch (error) {
    console.error(`Invalid URL: ${url} - error: ${error}aaaaaa`);
    return '';
  }
};

export const removeLastSlash = (value: string) => {
  return value.replace(/^https?:\/\//, '').replace(/\/$/, '');
};
