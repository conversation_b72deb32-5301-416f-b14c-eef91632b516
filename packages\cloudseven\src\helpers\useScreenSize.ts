import { useEffect, useState } from 'react';

const getScreenSize = () => {
  const width = window.innerWidth;

  if (width >= 1536) {
    return '2xl';
  }

  if (width >= 1280) {
    return 'xl';
  }

  if (width >= 1024) {
    return 'lg';
  }

  if (width >= 768) {
    return 'md';
  }

  if (width >= 640) {
    return 'sm';
  }

  return 'mobile';
};

const useScreenSize = () => {
  const [screenSize, setScreenSize] = useState<
    'mobile' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  >('2xl');
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    function checkScreenSize() {
      const screenSize = getScreenSize();
      setScreenSize(screenSize);

      const width = window.innerWidth;
      // Determine if it's mobile (tablet or phone) or not
      setIsMobile(width < 1024); // Consider mobile for anything below 1024px
    }

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return { screenSize, isMobile };
};

export default useScreenSize;
