import type { Locale } from '@hi7/lib/i18n';
import type { ProductSectionId, ServiceSectionId } from './info-card';

export type i18n = {
  locale: Locale;
};

export type DictionaryProps = {
  locale?: Locale;
  brand?: ProductSectionId;
  id?: string;
  slug?: ServiceSectionId;
};

export type LocaleProps = {
  params: {
    locale: Locale;
    id?: string;
    brand?: ProductSectionId;
    slug?: ServiceSectionId;
  };
};
