import { convertObjectToURLParams } from '@hi7/helpers/string';
import type { WP_REST_API_Post } from 'wp-types';

type PostsRequest = {
  page?: number;
  per_page?: number;
};

const DEFAULT_QUERY: PostsRequest = {
  page: 1,
  per_page: 10,
};

export const getPostsRequestUrl = (query?: PostsRequest) => {
  const urlParams = convertObjectToURLParams({
    ...DEFAULT_QUERY,
    ...query,
  });

  return `${process.env.WP_API_URL}/posts?${urlParams.toString()}`;
};

export const getFetchPosts = async (
  query?: PostsRequest,
): Promise<Response> => {
  const response = await fetch(getPostsRequestUrl(query), {
    next: {
      revalidate: 5 * 60, // 5 minutes in seconds
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch posts: ${response.statusText}`);
  }

  return response.json();
};

export const fetchAllPosts = async (
  sizePerFetch = 100,
): Promise<WP_REST_API_Post[]> => {
  try {
    // fetch page total page count
    const response = await fetch(
      getPostsRequestUrl({ per_page: sizePerFetch }),
      {
        next: {
          revalidate: 5 * 60, // 5 minutes in seconds
        },
      },
    );

    const totalPages = parseInt(
      response.headers.get('X-WP-TotalPages') as string,
    );

    // construct all requests
    const postRequests: Promise<Response>[] = [];

    for (let page = 0; page < totalPages; page++) {
      const pendingRequest = getFetchPosts({
        page: page + 1,
        per_page: sizePerFetch,
      });

      postRequests.push(pendingRequest);
    }

    // fetching all requests
    const settledRequests = await Promise.allSettled(postRequests);
    const requestError = settledRequests.find(
      (res) => res.status !== 'fulfilled',
    );

    if (requestError) {
      throw new Error(`Unable to fetch post: ${requestError.reason}`);
    }

    const responses = settledRequests
      .map((promise) => (promise.status === 'fulfilled' ? promise.value : null))
      .filter((e) => e !== null);

    // transform and flatten chunked posts
    return responses.flat() as unknown as WP_REST_API_Post[];
  } catch (error) {
    throw new Error(`Failed to fetch all posts ${error}`);
  }
};
