import { post } from '@hi7/helpers/request';
import type { FormValues } from '@hi7/interface/send-email';
import type { Locale } from '@hi7/lib/i18n';
import { useMutation, type UseMutationOptions } from '@tanstack/react-query';

type MailProps = FormValues & {
  company?: string;
  locale: Locale;
};
const sendEmail = async (data: MailProps) => {
  const { locale, ...params } = data;
  return post<void, FormValues>(`/email/send/BAYMAX_ENQUIRY`, params, locale);
};

export const useSendEmail = ({
  onSuccess,
  onError,
}: Omit<UseMutationOptions<void, Error, MailProps>, 'mutationFn'>) =>
  useMutation({
    mutationFn: sendEmail,
    onSuccess,
    onError,
  });
