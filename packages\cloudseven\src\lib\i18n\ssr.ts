import zh from '@hi7/dictionaries/zh.json';
import { Locale } from '.';

export const t = (locale: Locale) => {
  if (locale === 'en') {
    return (key: string) => {
      if (key.startsWith(`__SKIP_${locale}_`)) {
        return '';
      }
      return key;
    };
  }

  return (key: string, preset = '') => {
    const presetDict = zh[preset as keyof typeof zh] || zh;
    const translated = presetDict[key as keyof typeof presetDict] || key;
    return translated;
  };
};
