import { create } from 'zustand';

interface PaginationStore {
  currentPage: number;
  itemsPerPageOptions: number[];
  selectedItemsPerPage: number;
  setCurrentPage: (page: number) => void;
  setSelectedItemsPerPage: (items: number) => void;
  resetPagination: () => void;
}

const DEFAULT_VALUES = {
  currentPage: 1,
  itemsPerPageOptions: Array(5)
    .fill(0)
    .map((s, idx) => (idx + 1) * 12), // example options for items per page
  selectedItemsPerPage: 12, // default items per page
};

const usePaginationStore = create<PaginationStore>((set) => ({
  ...DEFAULT_VALUES,
  setCurrentPage: (page) => set({ currentPage: page }),
  setSelectedItemsPerPage: (items) =>
    set({ selectedItemsPerPage: items, currentPage: 1 }), // reset to page 1
  resetPagination: () => set({ ...DEFAULT_VALUES }),
}));

export default usePaginationStore;
