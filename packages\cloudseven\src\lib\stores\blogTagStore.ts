import { create } from 'zustand';

interface TagStore {
  selectedTags: string[];
  toggleTag: (tag: string) => void;
  resetTags: () => void;
}

const useTagStore = create<TagStore>((set) => ({
  selectedTags: [],

  // Toggle a tag in the selected tags list
  toggleTag: (tag) =>
    set((state) => ({
      selectedTags: state.selectedTags.includes(tag)
        ? state.selectedTags.filter((t) => t !== tag)
        : [...state.selectedTags, tag],
    })),

  // Reset all selected tags
  resetTags: () => set({ selectedTags: [] }),
}));

export default useTagStore;
