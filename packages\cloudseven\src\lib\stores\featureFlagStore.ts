import type { FeatureFlag } from '@hi7/interface/feature-flag';
import { create } from 'zustand';

type FeatureFlagState = {
  featureFlag: FeatureFlag;
};

type FeatureFlagAction = {
  init: (featureFlag: FeatureFlag) => void;
};

type FeatureFlagStore = FeatureFlagState & FeatureFlagAction;

const useFeatureFlagStore = create<FeatureFlagStore>((set) => ({
  featureFlag: {
    'feat-80247500': false,
    'feat-80264622': false,
  },

  init: (featureFlag) => set({ featureFlag }),
}));

export default useFeatureFlagStore;
