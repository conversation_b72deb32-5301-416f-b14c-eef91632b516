'use client';

import { createContext, ReactNode, useContext, useState } from 'react';

type HeaderProviderProps = {
  children?: ReactNode;
};

type HeaderContextType = {
  activeMenu: string;
  openMenu: (key: string) => void;
  closeMenu: () => void;
};

const HeaderContext = createContext<HeaderContextType | undefined>(undefined);

export const useHeader = () => {
  const context = useContext(HeaderContext);
  if (context === undefined) {
    throw new Error('useHeader must be used within a HeaderProvider');
  }
  return context;
};

const HeaderProvider = ({ children }: HeaderProviderProps) => {
  const [activeMenu, setActiveMenu] = useState<string>('');

  const openMenu = (key: string) => {
    setActiveMenu((prev) => (prev === key ? '' : key));
  };
  const closeMenu = () => setActiveMenu('');

  return (
    <HeaderContext.Provider
      value={{
        activeMenu,
        openMenu,
        closeMenu,
      }}
    >
      {children}
    </HeaderContext.Provider>
  );
};

export default HeaderProvider;
