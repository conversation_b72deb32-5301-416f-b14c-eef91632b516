'use client';

import { i18n } from '@hi7/interface/i18n';
import React, { createContext, useContext } from 'react';

type LocaleProviderProps = {
  children?: React.ReactNode;
  locale: i18n['locale'];
};

const LocaleContext = createContext<i18n['locale'] | undefined>(undefined);

export const useLocale = () => {
  const context = useContext(LocaleContext);
  if (context === undefined) {
    throw new Error('useLocale must be used within a LocaleProvider');
  }
  return context;
};

const LocaleProvider = ({ children, locale }: LocaleProviderProps) => {
  return (
    <LocaleContext.Provider value={locale}>{children}</LocaleContext.Provider>
  );
};

export default LocaleProvider;
