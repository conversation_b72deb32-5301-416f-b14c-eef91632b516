'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import type React from 'react';

type ProvidersProps = {
  children: React.ReactNode;
};

const queryClient = new QueryClient(); // Create the client instance once

const QueryClientProviders: React.FC<ProvidersProps> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

export default QueryClientProviders;
